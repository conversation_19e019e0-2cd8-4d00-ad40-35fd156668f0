package com.example.floatingfeishu;

import android.util.Log;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.json.JSONObject;

import java.io.IOException;

public class FeishuHelper {

    private static FeishuHelper instance;
    private final OkHttpClient client;
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private FeishuHelper() {
        client = new OkHttpClient();
    }

    public static synchronized FeishuHelper getInstance() {
        if (instance == null) {
            instance = new FeishuHelper();
        }
        return instance;
    }

    public void sendMessage(String webhookUrl, String content, boolean mentionAll) {
        new Thread(() -> {
            try {
                JSONObject message = buildMessage(content, mentionAll);
                RequestBody body = RequestBody.create(message.toString(), JSON);
                Request request = new Request.Builder()
                        .url(webhookUrl)
                        .post(body)
                        .build();

                try (Response response = client.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        Log.e("FeishuHelper", "发送失败: " + response.code() + " - " + response.body().string());
                    } else {
                        Log.i("FeishuHelper", "发送成功: " + response.code());
                    }

                }
            } catch (Exception e) {
                Log.e("FeishuHelper", "发送消息时发生异常", e);
            }
        }).start();
    }

    JSONObject buildMessage(String content, boolean mentionAll) {
        JSONObject msg = new JSONObject();
        try {
            msg.put("msg_type", "text");

            JSONObject contentObj = new JSONObject();
            contentObj.put("text", mentionAll ? "<at user_id=\"all\">所有人</at> " + content : content);

            msg.put("content", contentObj);
        } catch (Exception e)
        {
            Log.e("FeishuHelper", "构建消息体出错", e);
        }
        return msg;
    }
}
