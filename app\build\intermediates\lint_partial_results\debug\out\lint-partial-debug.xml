<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.0" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.floatingfeishu.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.layout.activity_main_enhanced"
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="386"
            endColumn="14"
            endOffset="15193"/>
        <entry
            name="model"
            string="color[colorPrimary(U),colorPrimaryDark(U),colorAccent(U),ic_launcher_background(U)],drawable[ic_launcher_foreground(U),bg_floating_ball(U)],id[bot_config_card(D),app_id_input(D),floating_ball(U),mode_radio_group(D),app_secret_input(D),test_bitable_button(D),webhook_mode_radio(D),automation_secret_input(U),automation_webhook_input(U),test_automation_button(U),bitable_enable_switch(D),chat_id_input(D),default_message_input(U),test_send_button(U),bitable_config_layout(D),save_automation_button(U),send_button(U),webhook_config_card(D),bitable_app_token_input(D),bot_mode_radio(D),automation_enabled_checkbox(U),save_config_button(U),webhook_url_input(U),ball_container(D),bitable_table_id_input(D)],layout[floating_ball(D),layout_floating_ball(U),simple_floating_ball(U),activity_main(U),activity_main_enhanced(D)],mipmap[ic_launcher_round(U),ic_launcher(U)],string[app_name(U)],style[Theme_FloatingFeishu(U),Theme_MaterialComponents_Light_DarkActionBar(R)];20^5,24^3^4,25^3^4,27^28^0^1^2;;;"/>
        <location id="R.layout.floating_ball"
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/floating_ball.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="38"
            endColumn="15"
            endOffset="1312"/>
    </map>

</incidents>
