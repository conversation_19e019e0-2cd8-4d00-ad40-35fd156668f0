<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.0" type="incidents">

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="53"
            column="14"
            startOffset="1784"
            endLine="53"
            endColumn="22"
            endOffset="1792"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="70"
            column="14"
            startOffset="2473"
            endLine="70"
            endColumn="22"
            endOffset="2481"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="135"
            column="14"
            startOffset="4908"
            endLine="135"
            endColumn="22"
            endOffset="4916"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="152"
            column="14"
            startOffset="5611"
            endLine="152"
            endColumn="22"
            endOffset="5619"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="108"
            column="18"
            startOffset="4123"
            endLine="108"
            endColumn="26"
            endOffset="4131"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="154"
            column="18"
            startOffset="5932"
            endLine="154"
            endColumn="26"
            endOffset="5940"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="171"
            column="18"
            startOffset="6677"
            endLine="171"
            endColumn="26"
            endOffset="6685"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="188"
            column="18"
            startOffset="7431"
            endLine="188"
            endColumn="26"
            endOffset="7439"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="232"
            column="18"
            startOffset="9119"
            endLine="232"
            endColumn="26"
            endOffset="9127"/>
    </incident>

    <incident
        id="UseSwitchCompatOrMaterialXml"
        severity="warning"
        message="Use `SwitchCompat` from AppCompat or `SwitchMaterial` from Material library">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="277"
            column="21"
            startOffset="10899"
            endLine="280"
            endColumn="64"
            endOffset="11094"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="298"
            column="22"
            startOffset="11768"
            endLine="298"
            endColumn="30"
            endOffset="11776"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="315"
            column="22"
            startOffset="12588"
            endLine="315"
            endColumn="30"
            endOffset="12596"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="13447"
                    endOffset="14714"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="13683"
                    endOffset="14175"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="14193"
                    endOffset="14685"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="345"
            column="18"
            startOffset="13684"
            endLine="345"
            endColumn="24"
            endOffset="13690"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="13447"
                    endOffset="14714"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="13683"
                    endOffset="14175"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="14193"
                    endOffset="14685"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="357"
            column="18"
            startOffset="14194"
            endLine="357"
            endColumn="24"
            endOffset="14200"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive icon is missing a monochrome tag">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="17"
            endOffset="267"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="17"
            endOffset="267"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/floatingfeishu/FloatingBallService.java"
            line="171"
            column="28"
            startOffset="5802"
            endLine="171"
            endColumn="35"
            endOffset="5809"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``ImageView`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/floatingfeishu/FloatingBallService.java"
            line="161"
            column="9"
            startOffset="5378"
            endLine="215"
            endColumn="11"
            endOffset="7787"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="12"
            column="9"
            startOffset="215"
            endLine="12"
            endColumn="21"
            endOffset="227"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/floatingfeishu/FloatingBallService.java"
            line="99"
            column="93"
            startOffset="3298"
            endLine="99"
            endColumn="97"
            endOffset="3302"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/floatingfeishu/FloatingBallService.java"
            line="104"
            column="97"
            startOffset="3531"
            endLine="104"
            endColumn="101"
            endOffset="3535"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.FloatingFeishu`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="6"
            column="5"
            startOffset="251"
            endLine="6"
            endColumn="33"
            endOffset="279"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#F5F5F5` with a theme that also paints a background (inferred theme is `@style/Theme.FloatingFeishu`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="7"
            column="5"
            startOffset="278"
            endLine="7"
            endColumn="33"
            endOffset="306"/>
    </incident>

</incidents>
