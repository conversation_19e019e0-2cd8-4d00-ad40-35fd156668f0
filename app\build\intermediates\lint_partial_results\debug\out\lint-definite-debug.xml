<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.0" type="incidents">

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;FloatingFeishu 自动化配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="18"
            column="9"
            startOffset="568"
            endLine="18"
            endColumn="44"
            endOffset="603"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;基础配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="41"
            column="17"
            startOffset="1352"
            endLine="41"
            endColumn="36"
            endOffset="1371"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Webhook URL:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="49"
            column="17"
            startOffset="1651"
            endLine="49"
            endColumn="44"
            endOffset="1678"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="53"
            column="14"
            startOffset="1784"
            endLine="53"
            endColumn="22"
            endOffset="1792"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入飞书机器人的Webhook URL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="57"
            column="17"
            startOffset="1966"
            endLine="57"
            endColumn="52"
            endOffset="2001"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;默认消息:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="66"
            column="17"
            startOffset="2347"
            endLine="66"
            endColumn="37"
            endOffset="2367"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="70"
            column="14"
            startOffset="2473"
            endLine="70"
            endColumn="22"
            endOffset="2481"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入默认发送的消息内容&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="74"
            column="17"
            startOffset="2659"
            endLine="74"
            endColumn="44"
            endOffset="2686"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;保存基础配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="85"
            column="17"
            startOffset="3126"
            endLine="85"
            endColumn="38"
            endOffset="3147"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;测试发送消息&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="93"
            column="17"
            startOffset="3443"
            endLine="93"
            endColumn="38"
            endOffset="3464"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;自动化流程配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="116"
            column="17"
            startOffset="4187"
            endLine="116"
            endColumn="39"
            endOffset="4209"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;启用自动化流程&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="125"
            column="17"
            startOffset="4551"
            endLine="125"
            endColumn="39"
            endOffset="4573"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;自动化Webhook URL:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="131"
            column="17"
            startOffset="4772"
            endLine="131"
            endColumn="47"
            endOffset="4802"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="135"
            column="14"
            startOffset="4908"
            endLine="135"
            endColumn="22"
            endOffset="4916"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入飞书自动化流程的Webhook URL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="139"
            column="17"
            startOffset="5097"
            endLine="139"
            endColumn="54"
            endOffset="5134"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;验证密钥 (可选):&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="148"
            column="17"
            startOffset="5480"
            endLine="148"
            endColumn="42"
            endOffset="5505"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="152"
            column="14"
            startOffset="5611"
            endLine="152"
            endColumn="22"
            endOffset="5619"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入验证密钥（用于签名验证）&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="156"
            column="17"
            startOffset="5799"
            endLine="156"
            endColumn="47"
            endOffset="5829"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;保存自动化配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="166"
            column="17"
            startOffset="6235"
            endLine="166"
            endColumn="39"
            endOffset="6257"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;测试自动化连接&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="174"
            column="17"
            startOffset="6559"
            endLine="174"
            endColumn="39"
            endOffset="6581"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;使用说明&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="196"
            column="17"
            startOffset="7258"
            endLine="196"
            endColumn="36"
            endOffset="7277"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;1. 配置基础的Webhook URL和默认消息&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="204"
            column="17"
            startOffset="7556"
            endLine="204"
            endColumn="56"
            endOffset="7595"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2. 启用自动化流程，配置自动化Webhook URL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="210"
            column="17"
            startOffset="7790"
            endLine="210"
            endColumn="59"
            endOffset="7832"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;3. 测试各项连接确保配置正确&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="216"
            column="17"
            startOffset="8027"
            endLine="216"
            endColumn="47"
            endOffset="8057"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;4. 悬浮球会显示在屏幕上&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="222"
            column="17"
            startOffset="8252"
            endLine="222"
            endColumn="45"
            endOffset="8280"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;5. 使用悬浮球发送消息，系统将自动记录到多维表格&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="228"
            column="17"
            startOffset="8475"
            endLine="228"
            endColumn="57"
            endOffset="8515"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;FloatingFeishu 配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="18"
            column="13"
            startOffset="604"
            endLine="18"
            endColumn="45"
            endOffset="636"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;配置模式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="42"
            column="21"
            startOffset="1504"
            endLine="42"
            endColumn="40"
            endOffset="1523"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Webhook模式（传统方式）&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="58"
            column="25"
            startOffset="2208"
            endLine="58"
            endColumn="55"
            endOffset="2238"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;应用机器人模式（推荐）&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="67"
            column="25"
            startOffset="2631"
            endLine="67"
            endColumn="51"
            endOffset="2657"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Webhook配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="95"
            column="21"
            startOffset="3595"
            endLine="95"
            endColumn="45"
            endOffset="3619"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Webhook URL:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="104"
            column="21"
            startOffset="3975"
            endLine="104"
            endColumn="48"
            endOffset="4002"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="108"
            column="18"
            startOffset="4123"
            endLine="108"
            endColumn="26"
            endOffset="4131"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入飞书机器人的Webhook URL&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="112"
            column="21"
            startOffset="4321"
            endLine="112"
            endColumn="56"
            endOffset="4356"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;应用机器人配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="141"
            column="21"
            startOffset="5404"
            endLine="141"
            endColumn="43"
            endOffset="5426"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;应用ID (App ID):&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="150"
            column="21"
            startOffset="5782"
            endLine="150"
            endColumn="50"
            endOffset="5811"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="154"
            column="18"
            startOffset="5932"
            endLine="154"
            endColumn="26"
            endOffset="5940"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入应用ID&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="158"
            column="21"
            startOffset="6125"
            endLine="158"
            endColumn="43"
            endOffset="6147"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;应用密钥 (App Secret):&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="167"
            column="21"
            startOffset="6523"
            endLine="167"
            endColumn="54"
            endOffset="6556"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="171"
            column="18"
            startOffset="6677"
            endLine="171"
            endColumn="26"
            endOffset="6685"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入应用密钥&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="175"
            column="21"
            startOffset="6874"
            endLine="175"
            endColumn="43"
            endOffset="6896"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;群组ID (Chat ID):&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="184"
            column="21"
            startOffset="7280"
            endLine="184"
            endColumn="51"
            endOffset="7310"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="188"
            column="18"
            startOffset="7431"
            endLine="188"
            endColumn="26"
            endOffset="7439"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入群组ID&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="192"
            column="21"
            startOffset="7625"
            endLine="192"
            endColumn="43"
            endOffset="7647"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;消息配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="219"
            column="21"
            startOffset="8603"
            endLine="219"
            endColumn="40"
            endOffset="8622"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;默认消息:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="228"
            column="21"
            startOffset="8978"
            endLine="228"
            endColumn="41"
            endOffset="8998"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="232"
            column="18"
            startOffset="9119"
            endLine="232"
            endColumn="26"
            endOffset="9127"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入默认发送的消息内容&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="236"
            column="21"
            startOffset="9321"
            endLine="236"
            endColumn="48"
            endOffset="9348"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;多维表格记录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="272"
            column="25"
            startOffset="10704"
            endLine="272"
            endColumn="46"
            endOffset="10725"/>
    </incident>

    <incident
        id="UseSwitchCompatOrMaterialXml"
        severity="warning"
        message="Use `SwitchCompat` from AppCompat or `SwitchMaterial` from Material library">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="277"
            column="21"
            startOffset="10899"
            endLine="280"
            endColumn="64"
            endOffset="11094"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;多维表格App Token:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="294"
            column="25"
            startOffset="11606"
            endLine="294"
            endColumn="54"
            endOffset="11635"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="298"
            column="22"
            startOffset="11768"
            endLine="298"
            endColumn="30"
            endOffset="11776"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入多维表格的App Token&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="302"
            column="25"
            startOffset="11988"
            endLine="302"
            endColumn="57"
            endOffset="12020"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;数据表Table ID:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="311"
            column="25"
            startOffset="12428"
            endLine="311"
            endColumn="52"
            endOffset="12455"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                dot="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="315"
            column="22"
            startOffset="12588"
            endLine="315"
            endColumn="30"
            endOffset="12596"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请输入数据表的Table ID&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="319"
            column="25"
            startOffset="12807"
            endLine="319"
            endColumn="55"
            endOffset="12837"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="13447"
                    endOffset="14714"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="13683"
                    endOffset="14175"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="14193"
                    endOffset="14685"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="345"
            column="18"
            startOffset="13684"
            endLine="345"
            endColumn="24"
            endOffset="13690"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;保存配置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="350"
            column="21"
            startOffset="13918"
            endLine="350"
            endColumn="40"
            endOffset="13937"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="13447"
                    endOffset="14714"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="13683"
                    endOffset="14175"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
                    startOffset="14193"
                    endOffset="14685"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="357"
            column="18"
            startOffset="14194"
            endLine="357"
            endColumn="24"
            endOffset="14200"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;测试发送&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="362"
            column="21"
            startOffset="14426"
            endLine="362"
            endColumn="40"
            endOffset="14445"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;测试多维表格连接&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="376"
            column="17"
            startOffset="14938"
            endLine="376"
            endColumn="40"
            endOffset="14961"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Floating Ball&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/floating_ball.xml"
            line="24"
            column="13"
            startOffset="870"
            endLine="24"
            endColumn="55"
            endOffset="912"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;发送&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/floating_ball.xml"
            line="32"
            column="9"
            startOffset="1123"
            endLine="32"
            endColumn="26"
            endOffset="1140"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Floating Ball&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/layout_floating_ball.xml"
            line="24"
            column="13"
            startOffset="889"
            endLine="24"
            endColumn="55"
            endOffset="931"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;发送&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/layout_floating_ball.xml"
            line="32"
            column="9"
            startOffset="1142"
            endLine="32"
            endColumn="26"
            endOffset="1159"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Floating Ball&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/simple_floating_ball.xml"
            line="23"
            column="13"
            startOffset="849"
            endLine="23"
            endColumn="55"
            endOffset="891"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;发送&quot;, should use `@string` resource">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/simple_floating_ball.xml"
            line="31"
            column="9"
            startOffset="1102"
            endLine="31"
            endColumn="26"
            endOffset="1119"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive icon is missing a monochrome tag">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="17"
            endOffset="267"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="17"
            endOffset="267"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/floatingfeishu/FloatingBallService.java"
            line="171"
            column="28"
            startOffset="5802"
            endLine="171"
            endColumn="35"
            endOffset="5809"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``ImageView`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/floatingfeishu/FloatingBallService.java"
            line="161"
            column="9"
            startOffset="5378"
            endLine="215"
            endColumn="11"
            endOffset="7787"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="12"
            column="9"
            startOffset="215"
            endLine="12"
            endColumn="21"
            endOffset="227"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/floatingfeishu/FloatingBallService.java"
            line="99"
            column="93"
            startOffset="3298"
            endLine="99"
            endColumn="97"
            endOffset="3302"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*sourceProvider*0*javaDir*0}/com/example/floatingfeishu/FloatingBallService.java"
            line="104"
            column="97"
            startOffset="3531"
            endLine="104"
            endColumn="101"
            endOffset="3535"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.FloatingFeishu`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="6"
            column="5"
            startOffset="251"
            endLine="6"
            endColumn="33"
            endOffset="279"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#F5F5F5` with a theme that also paints a background (inferred theme is `@style/Theme.FloatingFeishu`)">
        <location
            file="${:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml"
            line="7"
            column="5"
            startOffset="278"
            endLine="7"
            endColumn="33"
            endOffset="306"/>
    </incident>

</incidents>
