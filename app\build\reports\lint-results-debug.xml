<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.1.0">

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\build.gradle"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="InflateParams"
        severity="Warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        category="Correctness"
        priority="5"
        summary="Layout Inflation without a Parent"
        explanation="When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored."
        url="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        urls="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        errorLine1="            floatingView = LayoutInflater.from(this).inflate(R.layout.simple_floating_ball, null);"
        errorLine2="                                                                                            ~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\java\com\example\floatingfeishu\FloatingBallService.java"
            line="99"
            column="93"/>
    </issue>

    <issue
        id="InflateParams"
        severity="Warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        category="Correctness"
        priority="5"
        summary="Layout Inflation without a Parent"
        explanation="When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored."
        url="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        urls="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        errorLine1="                floatingView = LayoutInflater.from(this).inflate(R.layout.layout_floating_ball, null);"
        errorLine2="                                                                                                ~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\java\com\example\floatingfeishu\FloatingBallService.java"
            line="104"
            column="97"/>
    </issue>

    <issue
        id="UseSwitchCompatOrMaterialXml"
        severity="Warning"
        message="Use `SwitchCompat` from AppCompat or `SwitchMaterial` from Material library"
        category="Correctness"
        priority="5"
        summary="Replace usage of `Switch` widget"
        explanation="Use `SwitchCompat` from AppCompat or `SwitchMaterial` from Material library"
        errorLine1="                    &lt;Switch"
        errorLine2="                    ^">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="277"
            column="21"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.FloatingFeishu`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#f5f5f5&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#F5F5F5` with a theme that also paints a background (inferred theme is `@style/Theme.FloatingFeishu`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#F5F5F5&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.activity_main_enhanced` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;ScrollView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.floating_ball` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;FrameLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\floating_ball.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        severity="Warning"
        message="The application adaptive icon is missing a monochrome tag"
        category="Usability:Icons"
        priority="6"
        summary="Monochrome icon is not defined"
        explanation="If `android:roundIcon` and `android:icon` are both in your manifest, you must either remove the reference to `android:roundIcon` if it is not needed; or, supply the monochrome icon in the drawable defined by the `android:roundIcon` and `android:icon` attribute.&#xA;&#xA;For example, if `android:roundIcon` and `android:icon` are both in the manifest, a launcher might choose to use `android:roundIcon` over `android:icon` to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in `android:roundIcon`."
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="E:\FloatingFeishu\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        severity="Warning"
        message="The application adaptive roundIcon is missing a monochrome tag"
        category="Usability:Icons"
        priority="6"
        summary="Monochrome icon is not defined"
        explanation="If `android:roundIcon` and `android:icon` are both in your manifest, you must either remove the reference to `android:roundIcon` if it is not needed; or, supply the monochrome icon in the drawable defined by the `android:roundIcon` and `android:icon` attribute.&#xA;&#xA;For example, if `android:roundIcon` and `android:icon` are both in the manifest, a launcher might choose to use `android:roundIcon` over `android:icon` to display the adaptive app icon. Therefore, your themed application iconwill not show if your monochrome attribute is not also specified in `android:roundIcon`."
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="E:\FloatingFeishu\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://material.io/components/dialogs/"
        urls="https://material.io/components/dialogs/"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="345"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://material.io/components/dialogs/"
        urls="https://material.io/components/dialogs/"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="357"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="53"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="70"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="135"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="152"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="108"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="154"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="171"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="188"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="232"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="298"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        severity="Warning"
        message="Missing `autofillHints` attribute"
        category="Usability"
        priority="3"
        summary="Use Autofill"
        explanation="Specify an `autofillHints` attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.&#xA;&#xA;The hints can have any value, but it is recommended to use predefined values like &apos;username&apos; for a username or &apos;creditCardNumber&apos; for a credit card number. For a list of all predefined autofill hint constants, see the `AUTOFILL_HINT_` constants in the `View` reference at https://developer.android.com/reference/android/view/View.html.&#xA;&#xA;You can mark a view unimportant for autofill by specifying an `importantForAutofill` attribute on that view or a parent view. See https://developer.android.com/reference/android/view/View.html#setImportantForAutofill(int)."
        url="https://developer.android.com/guide/topics/text/autofill.html"
        urls="https://developer.android.com/guide/topics/text/autofill.html"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="315"
            column="22"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="Custom view ``ImageView`` has `setOnTouchListener` called on it but does not override `performClick`"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        floatingBall.setOnTouchListener(new View.OnTouchListener() {"
        errorLine2="        ^">
        <location
            file="E:\FloatingFeishu\app\src\main\java\com\example\floatingfeishu\FloatingBallService.java"
            line="161"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="`onTouch` should call `View#performClick` when a click is detected"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="            public boolean onTouch(View v, MotionEvent event) {"
        errorLine2="                           ~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\java\com\example\floatingfeishu\FloatingBallService.java"
            line="171"
            column="28"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;FloatingFeishu 自动化配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;FloatingFeishu 自动化配置&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="18"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;基础配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;基础配置&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="41"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Webhook URL:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;Webhook URL:&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="49"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入飞书机器人的Webhook URL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:hint=&quot;请输入飞书机器人的Webhook URL&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="57"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;默认消息:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;默认消息:&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="66"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入默认发送的消息内容&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:hint=&quot;请输入默认发送的消息内容&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="74"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;保存基础配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;保存基础配置&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="85"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;测试发送消息&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;测试发送消息&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="93"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;自动化流程配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;自动化流程配置&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="116"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;启用自动化流程&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;启用自动化流程&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="125"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;自动化Webhook URL:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;自动化Webhook URL:&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="131"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入飞书自动化流程的Webhook URL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:hint=&quot;请输入飞书自动化流程的Webhook URL&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="139"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;验证密钥 (可选):&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;验证密钥 (可选):&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="148"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入验证密钥（用于签名验证）&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:hint=&quot;请输入验证密钥（用于签名验证）&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="156"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;保存自动化配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;保存自动化配置&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="166"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;测试自动化连接&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;测试自动化连接&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="174"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;使用说明&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;使用说明&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="196"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;1. 配置基础的Webhook URL和默认消息&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;1. 配置基础的Webhook URL和默认消息&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="204"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;2. 启用自动化流程，配置自动化Webhook URL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;2. 启用自动化流程，配置自动化Webhook URL&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="210"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;3. 测试各项连接确保配置正确&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;3. 测试各项连接确保配置正确&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="216"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;4. 悬浮球会显示在屏幕上&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;4. 悬浮球会显示在屏幕上&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="222"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;5. 使用悬浮球发送消息，系统将自动记录到多维表格&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;5. 使用悬浮球发送消息，系统将自动记录到多维表格&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml"
            line="228"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;FloatingFeishu 配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;FloatingFeishu 配置&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;配置模式&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;配置模式&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="42"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Webhook模式（传统方式）&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;Webhook模式（传统方式）&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="58"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;应用机器人模式（推荐）&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;应用机器人模式（推荐）&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="67"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Webhook配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Webhook配置&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="95"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Webhook URL:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;Webhook URL:&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="104"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入飞书机器人的Webhook URL&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;请输入飞书机器人的Webhook URL&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="112"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;应用机器人配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;应用机器人配置&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="141"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;应用ID (App ID):&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;应用ID (App ID):&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="150"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入应用ID&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;请输入应用ID&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="158"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;应用密钥 (App Secret):&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;应用密钥 (App Secret):&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="167"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入应用密钥&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;请输入应用密钥&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="175"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;群组ID (Chat ID):&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;群组ID (Chat ID):&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="184"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入群组ID&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;请输入群组ID&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="192"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;消息配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;消息配置&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="219"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;默认消息:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;默认消息:&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="228"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入默认发送的消息内容&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:hint=&quot;请输入默认发送的消息内容&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="236"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;多维表格记录&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;多维表格记录&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="272"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;多维表格App Token:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;多维表格App Token:&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="294"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入多维表格的App Token&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;请输入多维表格的App Token&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="302"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;数据表Table ID:&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:text=&quot;数据表Table ID:&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="311"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;请输入数据表的Table ID&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                        android:hint=&quot;请输入数据表的Table ID&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="319"
            column="25"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;保存配置&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;保存配置&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="350"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;测试发送&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;测试发送&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="362"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;测试多维表格连接&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;测试多维表格连接&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml"
            line="376"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Floating Ball&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:contentDescription=&quot;Floating Ball&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\floating_ball.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;发送&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;发送&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\floating_ball.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Floating Ball&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:contentDescription=&quot;Floating Ball&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\layout_floating_ball.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;发送&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;发送&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\layout_floating_ball.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Floating Ball&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:contentDescription=&quot;Floating Ball&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\simple_floating_ball.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;发送&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;发送&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~">
        <location
            file="E:\FloatingFeishu\app\src\main\res\layout\simple_floating_ball.xml"
            line="31"
            column="9"/>
    </issue>

</issues>
