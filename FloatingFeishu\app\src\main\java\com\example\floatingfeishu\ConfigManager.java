package com.example.floatingfeishu;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Base64;

import java.nio.charset.StandardCharsets;
import java.security.Key;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;


public class ConfigManager {

    private static final String PREF_NAME = "FloatingFeishuConfig";
    private static final String KEY_WEBHOOK_URL = "webhook_url";
    private static final String KEY_DEFAULT_MESSAGE = "default_message";
    private static final String KEY_LAST_USED = "last_used";
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";

    private static ConfigManager instance;
    private final SharedPreferences sharedPreferences;
    private final Key secretKey;

    private ConfigManager(Context context) throws Exception {

        MasterKey masterKey = new MasterKey.Builder(context)
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build();

        sharedPreferences = EncryptedSharedPreferences.create(
                context,
                PREF_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        );

        secretKey = new SecretKeySpec("MySuperSecretKey".getBytes(), "AES"); // Replace with a secure key generation method
    }

    public static synchronized ConfigManager getInstance(Context context) throws Exception {
        if (instance == null) {
            instance = new ConfigManager(context);
        }
        return instance;
    }

    public void saveWebhookUrl(String url) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encrypted = cipher.doFinal(url.getBytes(StandardCharsets.UTF_8));
        String encoded = Base64.encodeToString(encrypted, Base64.DEFAULT);
        byte[] iv = cipher.getIV();
        String ivString = Base64.encodeToString(iv, Base64.DEFAULT);

        sharedPreferences.edit()
                .putString(KEY_WEBHOOK_URL, encoded)
                .putString("IV", ivString)
                .apply();
    }

    public String getWebhookUrl() throws Exception {
        String encoded = sharedPreferences.getString(KEY_WEBHOOK_URL, null);
        String ivString = sharedPreferences.getString("IV", null);

        if (encoded == null || ivString == null) {
            return null;
        }
        byte[] encrypted = Base64.decode(encoded, Base64.DEFAULT);
        byte[] iv = Base64.decode(ivString, Base64.DEFAULT);

        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey, new IvParameterSpec(iv));

        byte[] decrypted = cipher.doFinal(encrypted);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    public void saveDefaultMessage(String message) {
        sharedPreferences.edit().putString(KEY_DEFAULT_MESSAGE, message).apply();
    }

    public String getDefaultMessage() {
        return sharedPreferences.getString(KEY_DEFAULT_MESSAGE, null);
    }
    
    public void saveLastUsedTime(long time) {
        sharedPreferences.edit().putLong(KEY_LAST_USED, time).apply();
    }

    public long getLastUsedTime() {
        return sharedPreferences.getLong(KEY_LAST_USED, 0);
    }
}
