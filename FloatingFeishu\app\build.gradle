plugins {
    id 'com.android.application'
}

android {
    namespace 'com.example.floatingfeishu'
    compileSdk 30

    defaultConfig {
        applicationId "com.example.floatingfeishu"
        minSdk 21 // Android 5.0 (Lollipop)
        targetSdk 30
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        release {
            storeFile file('keystore.jks')
            storePassword 'floatingfeishu'
            keyAlias 'floatingfeishu'
            keyPassword 'floatingfeishu'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'androidx.security:security-crypto:1.1.0-alpha03'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
