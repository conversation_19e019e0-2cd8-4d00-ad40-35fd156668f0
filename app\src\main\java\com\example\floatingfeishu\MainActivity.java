package com.example.floatingfeishu;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.Switch;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

public class MainActivity extends AppCompatActivity {

    private ActivityResultLauncher<Intent> permissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // setContentView(R.layout.activity_main); // Remove setContentView here

        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (Settings.canDrawOverlays(this)) {
                            setupUI(); // Call setupUI after permission is granted
                        } else {
                            Toast.makeText(this, "权限被拒绝，无法显示悬浮窗", Toast.LENGTH_SHORT).show();
                        }
                    }
                }
        );

        checkOverlayPermission();
    }

    private void setupUI() {
        setContentView(R.layout.activity_main_enhanced); // 使用新的增强界面

        // 获取界面元素
        RadioGroup modeRadioGroup = findViewById(R.id.mode_radio_group);
        CardView webhookConfigCard = findViewById(R.id.webhook_config_card);
        CardView botConfigCard = findViewById(R.id.bot_config_card);

        EditText webhookUrlInput = findViewById(R.id.webhook_url_input);
        EditText appIdInput = findViewById(R.id.app_id_input);
        EditText appSecretInput = findViewById(R.id.app_secret_input);
        EditText chatIdInput = findViewById(R.id.chat_id_input);
        EditText defaultMessageInput = findViewById(R.id.default_message_input);

        // 多维表格相关元素
        Switch bitableEnableSwitch = findViewById(R.id.bitable_enable_switch);
        LinearLayout bitableConfigLayout = findViewById(R.id.bitable_config_layout);
        EditText bitableAppTokenInput = findViewById(R.id.bitable_app_token_input);
        EditText bitableTableIdInput = findViewById(R.id.bitable_table_id_input);

        Button saveConfigButton = findViewById(R.id.save_config_button);
        Button testSendButton = findViewById(R.id.test_send_button);
        Button testBitableButton = findViewById(R.id.test_bitable_button);

        try {
            ConfigManager configManager = ConfigManager.getInstance(this);

            if (configManager == null) {
                Toast.makeText(this, "初始化配置管理器失败", Toast.LENGTH_SHORT).show();
                return;
            }

            // 加载保存的配置
            boolean isBotMode = configManager.isBotMode();
            String savedWebhookUrl = configManager.getWebhookUrl();
            String savedAppId = configManager.getAppId();
            String savedAppSecret = configManager.getAppSecret();
            String savedChatId = configManager.getChatId();
            String savedDefaultMessage = configManager.getDefaultMessage();

            // 多维表格配置
            boolean bitableEnabled = configManager.isBitableEnabled();
            String savedBitableAppToken = configManager.getBitableAppToken();
            String savedBitableTableId = configManager.getBitableTableId();

            // 设置界面状态
            if (isBotMode) {
                modeRadioGroup.check(R.id.bot_mode_radio);
                webhookConfigCard.setVisibility(View.GONE);
                botConfigCard.setVisibility(View.VISIBLE);
            } else {
                modeRadioGroup.check(R.id.webhook_mode_radio);
                webhookConfigCard.setVisibility(View.VISIBLE);
                botConfigCard.setVisibility(View.GONE);
            }

            // 设置多维表格界面状态
            bitableEnableSwitch.setChecked(bitableEnabled);
            bitableConfigLayout.setVisibility(bitableEnabled ? View.VISIBLE : View.GONE);

            // 填充保存的数据
            if (savedWebhookUrl != null) {
                webhookUrlInput.setText(savedWebhookUrl);
            }
            if (savedAppId != null) {
                appIdInput.setText(savedAppId);
            }
            if (savedAppSecret != null) {
                appSecretInput.setText(savedAppSecret);
            }
            if (savedChatId != null) {
                chatIdInput.setText(savedChatId);
            }
            if (savedDefaultMessage != null) {
                defaultMessageInput.setText(savedDefaultMessage);
            }

            // 填充多维表格数据
            if (savedBitableAppToken != null) {
                bitableAppTokenInput.setText(savedBitableAppToken);
            }
            if (savedBitableTableId != null) {
                bitableTableIdInput.setText(savedBitableTableId);
            }

            // 模式切换监听器
            modeRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
                if (checkedId == R.id.webhook_mode_radio) {
                    webhookConfigCard.setVisibility(View.VISIBLE);
                    botConfigCard.setVisibility(View.GONE);
                } else if (checkedId == R.id.bot_mode_radio) {
                    webhookConfigCard.setVisibility(View.GONE);
                    botConfigCard.setVisibility(View.VISIBLE);
                }
            });

            // 多维表格开关监听器
            bitableEnableSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                bitableConfigLayout.setVisibility(isChecked ? View.VISIBLE : View.GONE);
            });

            // 保存配置按钮监听器
            saveConfigButton.setOnClickListener(v -> {
                try {
                    boolean currentBotMode = modeRadioGroup.getCheckedRadioButtonId() == R.id.bot_mode_radio;
                    String defaultMessage = defaultMessageInput.getText().toString().trim();

                    if (currentBotMode) {
                        // 应用机器人模式
                        String appId = appIdInput.getText().toString().trim();
                        String appSecret = appSecretInput.getText().toString().trim();
                        String chatId = chatIdInput.getText().toString().trim();

                        if (appId.isEmpty() || appSecret.isEmpty() || chatId.isEmpty()) {
                            Toast.makeText(this, "应用机器人模式下，所有字段都不能为空", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        configManager.setBotMode(true);
                        configManager.saveAppCredentials(appId, appSecret);
                        configManager.saveChatId(chatId);
                    } else {
                        // Webhook模式
                        String webhookUrl = webhookUrlInput.getText().toString().trim();

                        if (webhookUrl.isEmpty()) {
                            Toast.makeText(this, "Webhook URL不能为空", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        if (!webhookUrl.startsWith("http")) {
                            Toast.makeText(this, "Webhook URL必须以http或https开头", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        configManager.setBotMode(false);
                        configManager.saveWebhookUrl(webhookUrl);
                    }

                    configManager.saveDefaultMessage(defaultMessage);

                    // 保存多维表格配置
                    boolean currentBitableEnabled = bitableEnableSwitch.isChecked();
                    configManager.setBitableEnabled(currentBitableEnabled);

                    if (currentBitableEnabled) {
                        String bitableAppToken = bitableAppTokenInput.getText().toString().trim();
                        String bitableTableId = bitableTableIdInput.getText().toString().trim();

                        if (bitableAppToken.isEmpty() || bitableTableId.isEmpty()) {
                            Toast.makeText(this, "启用多维表格记录时，App Token和Table ID不能为空", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        configManager.saveBitableConfig(bitableAppToken, bitableTableId);
                    }

                    Toast.makeText(this, "配置已保存", Toast.LENGTH_SHORT).show();

                    // 重新启动悬浮球服务
                    startFloatingBallService();
                } catch (Exception e) {
                    Toast.makeText(this, "保存配置失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            });

            // 测试发送按钮监听器
            testSendButton.setOnClickListener(v -> {
                try {
                    boolean testBotMode = modeRadioGroup.getCheckedRadioButtonId() == R.id.bot_mode_radio;
                    String defaultMessage = defaultMessageInput.getText().toString().trim();

                    if (defaultMessage.isEmpty()) {
                        defaultMessage = "测试消息";
                    }

                    if (testBotMode) {
                        // 应用机器人模式测试
                        String appId = appIdInput.getText().toString().trim();
                        String appSecret = appSecretInput.getText().toString().trim();
                        String chatId = chatIdInput.getText().toString().trim();

                        if (appId.isEmpty() || appSecret.isEmpty() || chatId.isEmpty()) {
                            Toast.makeText(this, "请先填写完整的应用机器人配置", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        FeishuBotManager botManager = FeishuBotManager.getInstance(this);
                        botManager.setAppCredentials(appId, appSecret);

                        Toast.makeText(this, "正在发送测试消息...", Toast.LENGTH_SHORT).show();

                        botManager.sendMessageToGroup(chatId, defaultMessage, false, new FeishuBotManager.MessageCallback() {
                            @Override
                            public void onSuccess(String result) {
                                runOnUiThread(() -> Toast.makeText(MainActivity.this, "测试消息发送成功", Toast.LENGTH_SHORT).show());
                            }

                            @Override
                            public void onError(String error) {
                                runOnUiThread(() -> Toast.makeText(MainActivity.this, "测试消息发送失败: " + error, Toast.LENGTH_LONG).show());
                            }
                        });
                    } else {
                        // Webhook模式测试
                        String webhookUrl = webhookUrlInput.getText().toString().trim();

                        if (webhookUrl.isEmpty()) {
                            Toast.makeText(this, "请先填写Webhook URL", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        if (!webhookUrl.startsWith("http")) {
                            Toast.makeText(this, "Webhook URL格式不正确", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        Toast.makeText(this, "正在发送测试消息...", Toast.LENGTH_SHORT).show();
                        FeishuHelper.getInstance().sendMessage(webhookUrl, defaultMessage, false);
                        Toast.makeText(this, "测试消息已发送", Toast.LENGTH_SHORT).show();
                    }
                } catch (Exception e) {
                    Toast.makeText(this, "测试发送失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            });

            // 多维表格测试按钮监听器
            testBitableButton.setOnClickListener(v -> {
                try {
                    if (!bitableEnableSwitch.isChecked()) {
                        Toast.makeText(this, "请先启用多维表格记录功能", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    String bitableAppToken = bitableAppTokenInput.getText().toString().trim();
                    String bitableTableId = bitableTableIdInput.getText().toString().trim();

                    if (bitableAppToken.isEmpty() || bitableTableId.isEmpty()) {
                        Toast.makeText(this, "请先填写多维表格配置信息", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    // 临时保存配置用于测试
                    configManager.setBitableEnabled(true);
                    configManager.saveBitableConfig(bitableAppToken, bitableTableId);

                    Toast.makeText(this, "正在测试多维表格连接...", Toast.LENGTH_SHORT).show();

                    BitableManager bitableManager = BitableManager.getInstance(this);
                    bitableManager.testConnection(new BitableManager.RecordCallback() {
                        @Override
                        public void onSuccess(String result) {
                            runOnUiThread(() -> Toast.makeText(MainActivity.this, "多维表格连接测试成功", Toast.LENGTH_SHORT).show());
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> Toast.makeText(MainActivity.this, "多维表格连接测试失败: " + error, Toast.LENGTH_LONG).show());
                        }
                    });
                } catch (Exception e) {
                    Toast.makeText(this, "测试多维表格连接失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            });

        } catch (Exception e) {
            Toast.makeText(this, "初始化配置管理器失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }

        startFloatingBallService(); // Start the service after setting up the UI
    }

    private void checkOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + getPackageName()));
                permissionLauncher.launch(intent);
            } else {
                setupUI(); // Call setupUI if permission is already granted
            }
        } else {
            setupUI(); // Call setupUI for older Android versions
        }
    }

    private void startFloatingBallService() {
        try {
            // 再次检查权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                Toast.makeText(this, "需要悬浮窗权限才能启动悬浮球", Toast.LENGTH_LONG).show();
                checkOverlayPermission();
                return;
            }

            Intent intent = new Intent(this, FloatingBallService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent);
            } else {
                startService(intent);
            }
            // 显示提示
            Toast.makeText(this, "悬浮球已启动，可以切换到其他应用使用", Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            Toast.makeText(this, "启动悬浮球失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
            e.printStackTrace();
        }
    }
}
