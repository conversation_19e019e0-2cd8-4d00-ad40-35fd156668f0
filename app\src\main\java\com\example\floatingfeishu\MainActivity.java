package com.example.floatingfeishu;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.Switch;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.cardview.widget.CardView;

public class MainActivity extends AppCompatActivity {

    private ActivityResultLauncher<Intent> permissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // setContentView(R.layout.activity_main); // Remove setContentView here

        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (Settings.canDrawOverlays(this)) {
                            setupUI(); // Call setupUI after permission is granted
                        } else {
                            Toast.makeText(this, "权限被拒绝，无法显示悬浮窗", Toast.LENGTH_SHORT).show();
                        }
                    }
                }
        );

        checkOverlayPermission();
    }

    private void setupUI() {
        setContentView(R.layout.activity_main); // 使用更新后的主界面

        // 获取界面元素
        EditText webhookUrlInput = findViewById(R.id.webhook_url_input);
        EditText defaultMessageInput = findViewById(R.id.default_message_input);

        // 自动化配置元素
        CheckBox automationEnabledCheckbox = findViewById(R.id.automation_enabled_checkbox);
        EditText automationWebhookInput = findViewById(R.id.automation_webhook_input);
        EditText automationSecretInput = findViewById(R.id.automation_secret_input);

        Button saveConfigButton = findViewById(R.id.save_config_button);
        Button testSendButton = findViewById(R.id.test_send_button);
        Button saveAutomationButton = findViewById(R.id.save_automation_button);
        Button testAutomationButton = findViewById(R.id.test_automation_button);

        try {
            ConfigManager configManager = ConfigManager.getInstance(this);

            if (configManager == null) {
                Toast.makeText(this, "初始化配置管理器失败", Toast.LENGTH_SHORT).show();
                return;
            }

            // 加载保存的配置
            String savedWebhookUrl = configManager.getWebhookUrl();
            String savedDefaultMessage = configManager.getDefaultMessage();

            // 自动化配置
            boolean automationEnabled = configManager.isAutomationEnabled();
            String savedAutomationWebhook = configManager.getAutomationWebhookUrl();
            String savedAutomationSecret = configManager.getAutomationSecret();

            // 填充保存的数据
            if (savedWebhookUrl != null) {
                webhookUrlInput.setText(savedWebhookUrl);
            }
            if (savedDefaultMessage != null) {
                defaultMessageInput.setText(savedDefaultMessage);
            }

            // 填充自动化配置数据
            automationEnabledCheckbox.setChecked(automationEnabled);
            if (savedAutomationWebhook != null) {
                automationWebhookInput.setText(savedAutomationWebhook);
            }
            if (savedAutomationSecret != null) {
                automationSecretInput.setText(savedAutomationSecret);
            }

            // 这里可以添加其他UI监听器，目前使用简化的界面

            // 保存基础配置按钮监听器
            saveConfigButton.setOnClickListener(v -> {
                try {
                    String webhookUrl = webhookUrlInput.getText().toString().trim();
                    String defaultMessage = defaultMessageInput.getText().toString().trim();

                    if (webhookUrl.isEmpty()) {
                        Toast.makeText(this, "Webhook URL不能为空", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    if (!webhookUrl.startsWith("http")) {
                        Toast.makeText(this, "Webhook URL必须以http或https开头", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    configManager.saveWebhookUrl(webhookUrl);
                    configManager.saveDefaultMessage(defaultMessage);

                    Toast.makeText(this, "基础配置已保存", Toast.LENGTH_SHORT).show();

                    // 重新启动悬浮球服务
                    startFloatingBallService();
                } catch (Exception e) {
                    Toast.makeText(this, "保存配置失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            });

            // 保存自动化配置按钮监听器
            saveAutomationButton.setOnClickListener(v -> {
                try {
                    boolean currentAutomationEnabled = automationEnabledCheckbox.isChecked();
                    String automationWebhook = automationWebhookInput.getText().toString().trim();
                    String automationSecret = automationSecretInput.getText().toString().trim();

                    configManager.setAutomationEnabled(currentAutomationEnabled);

                    if (currentAutomationEnabled) {
                        if (automationWebhook.isEmpty()) {
                            Toast.makeText(this, "启用自动化时，Webhook URL不能为空", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        if (!automationWebhook.startsWith("http")) {
                            Toast.makeText(this, "自动化Webhook URL必须以http或https开头", Toast.LENGTH_SHORT).show();
                            return;
                        }

                        configManager.saveAutomationWebhookUrl(automationWebhook);
                        configManager.saveAutomationSecret(automationSecret);
                    }

                    Toast.makeText(this, "自动化配置已保存", Toast.LENGTH_SHORT).show();
                } catch (Exception e) {
                    Toast.makeText(this, "保存自动化配置失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            });

            // 测试发送按钮监听器
            testSendButton.setOnClickListener(v -> {
                try {
                    String webhookUrl = webhookUrlInput.getText().toString().trim();
                    String defaultMessage = defaultMessageInput.getText().toString().trim();

                    if (defaultMessage.isEmpty()) {
                        defaultMessage = "测试消息";
                    }

                    if (webhookUrl.isEmpty()) {
                        Toast.makeText(this, "请先填写Webhook URL", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    if (!webhookUrl.startsWith("http")) {
                        Toast.makeText(this, "Webhook URL格式不正确", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    Toast.makeText(this, "正在发送测试消息...", Toast.LENGTH_SHORT).show();
                    FeishuHelper.getInstance().sendMessage(webhookUrl, defaultMessage, false);
                    Toast.makeText(this, "测试消息已发送", Toast.LENGTH_SHORT).show();
                } catch (Exception e) {
                    Toast.makeText(this, "测试发送失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            });

            // 测试自动化按钮监听器
            testAutomationButton.setOnClickListener(v -> {
                try {
                    if (!automationEnabledCheckbox.isChecked()) {
                        Toast.makeText(this, "请先启用自动化流程", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    String automationWebhook = automationWebhookInput.getText().toString().trim();
                    if (automationWebhook.isEmpty()) {
                        Toast.makeText(this, "请先填写自动化Webhook URL", Toast.LENGTH_SHORT).show();
                        return;
                    }

                    // 临时保存配置用于测试
                    configManager.setAutomationEnabled(true);
                    configManager.saveAutomationWebhookUrl(automationWebhook);
                    String secret = automationSecretInput.getText().toString().trim();
                    if (!secret.isEmpty()) {
                        configManager.saveAutomationSecret(secret);
                    }

                    Toast.makeText(this, "正在测试自动化连接...", Toast.LENGTH_SHORT).show();

                    FeishuAutomationHelper automationHelper = FeishuAutomationHelper.getInstance(this);
                    automationHelper.testAutomationConnection(new FeishuAutomationHelper.TestCallback() {
                        @Override
                        public void onSuccess(String message) {
                            runOnUiThread(() -> Toast.makeText(MainActivity.this, "自动化连接测试成功", Toast.LENGTH_SHORT).show());
                        }

                        @Override
                        public void onError(String error) {
                            runOnUiThread(() -> Toast.makeText(MainActivity.this, "自动化连接测试失败: " + error, Toast.LENGTH_LONG).show());
                        }
                    });
                } catch (Exception e) {
                    Toast.makeText(this, "测试自动化连接失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            });



        } catch (Exception e) {
            Toast.makeText(this, "初始化配置管理器失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }

        startFloatingBallService(); // Start the service after setting up the UI
    }

    private void checkOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + getPackageName()));
                permissionLauncher.launch(intent);
            } else {
                setupUI(); // Call setupUI if permission is already granted
            }
        } else {
            setupUI(); // Call setupUI for older Android versions
        }
    }

    private void startFloatingBallService() {
        try {
            // 再次检查权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                Toast.makeText(this, "需要悬浮窗权限才能启动悬浮球", Toast.LENGTH_LONG).show();
                checkOverlayPermission();
                return;
            }

            Intent intent = new Intent(this, FloatingBallService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent);
            } else {
                startService(intent);
            }
            // 显示提示
            Toast.makeText(this, "悬浮球已启动，可以切换到其他应用使用", Toast.LENGTH_LONG).show();
        } catch (Exception e) {
            Toast.makeText(this, "启动悬浮球失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
            e.printStackTrace();
        }
    }
}
