<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="FloatingFeishu 配置"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- 模式选择卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="配置模式"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <RadioGroup
                    android:id="@+id/mode_radio_group"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <RadioButton
                        android:id="@+id/webhook_mode_radio"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Webhook模式（传统方式）"
                        android:textSize="16sp"
                        android:checked="true"
                        android:layout_marginBottom="8dp" />

                    <RadioButton
                        android:id="@+id/bot_mode_radio"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="应用机器人模式（推荐）"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp" />

                </RadioGroup>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Webhook模式配置 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/webhook_config_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Webhook配置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Webhook URL:"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/webhook_url_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="请输入飞书机器人的Webhook URL"
                    android:inputType="textUri"
                    android:layout_marginBottom="16dp"
                    android:background="@android:drawable/edit_text"
                    android:padding="12dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 应用机器人模式配置 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/bot_config_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="应用机器人配置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="应用ID (App ID):"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/app_id_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="请输入应用ID"
                    android:inputType="text"
                    android:layout_marginBottom="12dp"
                    android:background="@android:drawable/edit_text"
                    android:padding="12dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="应用密钥 (App Secret):"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/app_secret_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="请输入应用密钥"
                    android:inputType="textPassword"
                    android:layout_marginBottom="12dp"
                    android:background="@android:drawable/edit_text"
                    android:padding="12dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="群组ID (Chat ID):"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/chat_id_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="请输入群组ID"
                    android:inputType="text"
                    android:layout_marginBottom="16dp"
                    android:background="@android:drawable/edit_text"
                    android:padding="12dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 通用配置 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="消息配置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="默认消息:"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <EditText
                    android:id="@+id/default_message_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="请输入默认发送的消息内容"
                    android:inputType="textMultiLine"
                    android:lines="3"
                    android:gravity="top"
                    android:background="@android:drawable/edit_text"
                    android:padding="12dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 多维表格配置 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="多维表格记录"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#333333" />

                    <Switch
                        android:id="@+id/bitable_enable_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/bitable_config_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="多维表格App Token:"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <EditText
                        android:id="@+id/bitable_app_token_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="请输入多维表格的App Token"
                        android:inputType="text"
                        android:layout_marginBottom="12dp"
                        android:background="@android:drawable/edit_text"
                        android:padding="12dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="数据表Table ID:"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <EditText
                        android:id="@+id/bitable_table_id_input"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="请输入数据表的Table ID"
                        android:inputType="text"
                        android:layout_marginBottom="16dp"
                        android:background="@android:drawable/edit_text"
                        android:padding="12dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="16dp">

            <!-- 第一行按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <Button
                    android:id="@+id/save_config_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="保存配置"
                    android:textSize="16sp"
                    android:textColor="#FFFFFF"
                    android:background="#4CAF50"
                    android:layout_marginEnd="8dp"
                    android:padding="12dp" />

                <Button
                    android:id="@+id/test_send_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="测试发送"
                    android:textSize="16sp"
                    android:textColor="#FFFFFF"
                    android:background="#2196F3"
                    android:layout_marginStart="8dp"
                    android:padding="12dp" />

            </LinearLayout>

            <!-- 第二行按钮 -->
            <Button
                android:id="@+id/test_bitable_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试多维表格连接"
                android:textSize="16sp"
                android:textColor="#FFFFFF"
                android:background="#FF9800"
                android:padding="12dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
