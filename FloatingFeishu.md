好的，用户让我写一个开发者文档，我需要先理解他之前的需求和已经提供的步骤。用户之前想要创建一个悬浮球应用，关联飞书机器人的Webhook，并自定义快捷键。我之前给出了详细的步骤和代码示例，现在用户需要更正式的文档。

首先，开发者文档通常包括概述、功能列表、快速开始指南、详细实现说明、API参考、常见问题等部分。我需要根据之前的对话内容整理这些部分。

用户可能希望文档结构清晰，方便其他开发者理解和使用项目。需要确保技术细节准确，特别是权限配置、服务实现和网络请求部分。同时，要包含配置存储和消息发送的实现细节，因为这些是核心功能。

考虑到用户之前的问题，可能还需要加入自定义快捷键的部分，虽然之前的步骤中提到了菜单选项，但可能需要更详细的说明。另外，需要确保文档中的代码示例与之前的回答一致，避免矛盾。

可能还需要注意事项部分，比如Android版本适配、权限处理、安全性建议等。扩展功能部分可以给开发者提供进一步开发的思路，比如支持其他平台或消息格式。

最后，需要检查文档是否覆盖所有必要的信息，比如环境要求、依赖库、配置方法等，确保其他开发者能够顺利构建和运行项目。同时，语言要专业但不过于复杂，保持技术文档的清晰和实用性。




以下是完整的开发者文档：

---

# FloatingFeishu 开发者文档

## 1. 项目概述
一款支持自定义Webhook的悬浮球工具，深度集成飞书机器人API，实现快速消息推送。支持Android 5.0+系统，主要功能模块包括：

• 全局悬浮窗控制
• Webhook配置管理
• 即时消息推送
• 可扩展操作菜单

## 2. 技术架构
```mermaid
graph TD
    A[悬浮球服务] --> B[窗口管理]
    A --> C[触摸事件处理]
    D[配置模块] --> E[SharedPreferences]
    F[网络模块] --> G[OkHttp]
    H[飞书机器人] --> I[Webhook接口]
    C --> J[菜单系统]
    J --> K[消息发送]
```

## 3. 核心功能实现

### 3.1 悬浮窗服务
**关键类**：`FloatingBallService`

**实现要点**：
```java
// 窗口类型适配
int windowType = (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) 
    ? WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY 
    : WindowManager.LayoutParams.TYPE_PHONE;

// 拖拽算法
params.x = initialX + (int)(currentX - initialTouchX);
params.y = initialY + (int)(currentY - initialTouchY);
windowManager.updateViewLayout(view, params);
```

### 3.2 Webhook配置管理
**数据存储结构**：
```json
{
  "webhook_url": "https://open.feishu.cn/open-apis/bot/v2/hook/xxx",
  "default_message": "紧急通知！",
  "last_used": 1659878400
}
```

**加密存储示例**：
```java
// AES加密存储
Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
cipher.init(Cipher.ENCRYPT_MODE, secretKey);
byte[] encrypted = cipher.doFinal(url.getBytes());
```

### 3.3 消息发送协议
**飞书消息体结构**：
```java
JSONObject buildMessage(String content, boolean mentionAll) {
    JSONObject msg = new JSONObject();
    msg.put("msg_type", "text");
    
    JSONObject contentObj = new JSONObject();
    contentObj.put("text", mentionAll ? "<at user_id=\"all\">所有人</at> " + content : content);
    
    msg.put("content", contentObj);
    return msg;
}
```

## 4. API 参考

### 4.1 悬浮窗服务API
| 方法 | 参数 | 说明 |
|------|------|------|
| `startService()` | Context | 启动悬浮窗服务 |
| `updatePosition()` | x, y | 更新悬浮球位置 |
| `toggleVisibility()` | boolean | 显示/隐藏悬浮球 |

### 4.2 飞书接口API
```http
POST https://open.feishu.cn/open-apis/bot/v2/hook/{key}
Content-Type: application/json

{
  "msg_type": "text",
  "content": {
    "text": "消息内容"
  }
}
```

## 5. 配置指南

### 5.1 环境要求
• Android Studio Arctic Fox+
• JDK 11
• Android SDK 30+

### 5.2 依赖库
```gradle
dependencies {
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'androidx.security:security-crypto:1.1.0-alpha03'
}
```

### 5.3 权限配置
在`AndroidManifest.xml`中声明：
```xml
<!-- 必须权限 -->
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

<!-- 可选权限 -->
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

## 6. 扩展开发

### 6.1 添加新平台支持
1. 继承`BaseMessageSender`
```java
public class DingTalkSender extends BaseMessageSender {
    @Override
    protected String buildRequestBody(String content) {
        // 实现钉钉消息格式
    }
}
```

2. 更新配置界面
```xml
<RadioGroup>
    <RadioButton 
        android:id="@+id/rb_feishu"
        android:text="飞书"/>
    <RadioButton 
        android:id="@+id/rb_dingtalk" 
        android:text="钉钉"/>
</RadioGroup>
```

### 6.2 自定义快捷键
**快捷键配置示例**：
```java
public class ShortcutManager {
    private static final Map<Integer, String> SHORTCUTS = new HashMap<>();
    
    static {
        SHORTCUTS.put(KeyEvent.KEYCODE_VOLUME_UP, "SEND_MESSAGE");
        SHORTCUTS.put(KeyEvent.KEYCODE_CAMERA, "OPEN_SETTINGS");
    }
    
    public void handleKeyEvent(KeyEvent event) {
        // 处理物理按键事件
    }
}
```

## 7. 测试方案

### 7.1 单元测试用例
```java
@Test
public void testWebhookValidation() {
    assertTrue(UrlValidator.isValidFeishuWebhook(
        "https://open.feishu.cn/open-apis/bot/v2/hook/123456"));
    
    assertFalse(UrlValidator.isValidFeishuWebhook(
        "http://malicious.site"));
}
```

### 7.2 集成测试流程
1. 启动应用并授予权限
2. 配置有效Webhook地址
3. 触发消息发送操作
4. 验证飞书群组接收情况
5. 测试异常场景（无效URL、无网络等）

## 8. 发布说明

### 8.1 版本记录
| 版本 | 日期 | 特性 |
|------|------|------|
| 1.0.0 | 2023-08-20 | 基础消息发送功能 |
| 1.1.0 | 2023-09-01 | 增加多平台支持 |

### 8.2 构建命令
```bash
./gradlew assembleRelease
# 输出路径：app/build/outputs/apk/release/
```

## 9. 常见问题

### Q1: 悬浮窗无法显示
**解决方案**：
1. 检查是否授予`SYSTEM_ALERT_WINDOW`权限
2. 验证WindowManager是否正确初始化
3. 查看Logcat中的窗口添加错误日志

### Q2: 消息发送失败但网络正常
**排查步骤**：
```text
1. 检查Webhook地址格式是否正确
2. 使用curl测试接口可用性
3. 查看OkHttp网络日志：
   adb logcat | grep FeishuHelper
```

## 10. 安全规范

1. Webhook地址存储必须加密
2. 网络请求强制使用HTTPS
3. 用户输入内容需做HTML转义
4. 定期轮换加密密钥

---

该文档可作为项目的基础技术参考，建议结合源码中的具体实现进行扩展。对于需要深度定化的功能模块，建议创建独立的模块文档进行详细说明。