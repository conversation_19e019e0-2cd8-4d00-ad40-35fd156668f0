{"logs": [{"outputFile": "com.example.floatingfeishu.app-mergeReleaseResources-30:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecca92267eba45ce3e43e495f0e1965d\\transformed\\material-1.10.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1071,1174,1249,1312,1404,1475,1540,1607,1679,1751,1805,1926,1985,2049,2103,2180,2312,2397,2478,2627,2714,2797,2939,3031,3109,3165,3223,3289,3361,3438,3529,3612,3692,3771,3846,3925,4029,4119,4192,4286,4383,4457,4530,4629,4684,4768,4836,4924,5013,5075,5139,5202,5273,5382,5493,5596,5704,5764,5826", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "266,344,421,498,592,680,792,918,999,1066,1169,1244,1307,1399,1470,1535,1602,1674,1746,1800,1921,1980,2044,2098,2175,2307,2392,2473,2622,2709,2792,2934,3026,3104,3160,3218,3284,3356,3433,3524,3607,3687,3766,3841,3920,4024,4114,4187,4281,4378,4452,4525,4624,4679,4763,4831,4919,5008,5070,5134,5197,5268,5377,5488,5591,5699,5759,5821,5903"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3162,3239,3333,3421,3533,3659,3740,3807,3910,3985,4048,4140,4211,4276,4343,4415,4487,4541,4662,4721,4785,4839,4916,5048,5133,5214,5363,5450,5533,5675,5767,5845,5901,5959,6025,6097,6174,6265,6348,6428,6507,6582,6661,6765,6855,6928,7022,7119,7193,7266,7365,7420,7504,7572,7660,7749,7811,7875,7938,8009,8118,8229,8332,8440,8500,8562", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "316,3080,3157,3234,3328,3416,3528,3654,3735,3802,3905,3980,4043,4135,4206,4271,4338,4410,4482,4536,4657,4716,4780,4834,4911,5043,5128,5209,5358,5445,5528,5670,5762,5840,5896,5954,6020,6092,6169,6260,6343,6423,6502,6577,6656,6760,6850,6923,7017,7114,7188,7261,7360,7415,7499,7567,7655,7744,7806,7870,7933,8004,8113,8224,8327,8435,8495,8557,8639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38fdf4e56534b3c4c6a8297bf7eeddee\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,536,648,736,839,954,1033,1110,1201,1294,1389,1483,1583,1676,1771,1865,1956,2049,2130,2234,2337,2435,2542,2649,2754,2911,8644", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "424,531,643,731,834,949,1028,1105,1196,1289,1384,1478,1578,1671,1766,1860,1951,2044,2125,2229,2332,2430,2537,2644,2749,2906,3002,8721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cf89ab1ea7cc19352d4f47103c74320d\\transformed\\core-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "8726", "endColumns": "100", "endOffsets": "8822"}}]}]}