<?xml version="1.0" encoding="UTF-8"?>
<lint>
    <!-- 抑制SharedPreferences.Editor#apply的API级别警告 -->
    <issue id="NewApi" severity="ignore">
        <ignore path="**/ConfigManager.java" />
    </issue>

    <!-- 抑制未使用资源的警告 -->
    <issue id="UnusedResources" severity="ignore" />

    <!-- 抑制硬编码文本的警告（对于测试和示例代码） -->
    <issue id="HardcodedText" severity="ignore" />

    <!-- 抑制图标缺失的警告 -->
    <issue id="IconMissingDensityFolder" severity="ignore" />

    <!-- 抑制权限相关的警告 -->
    <issue id="MissingPermission" severity="warning" />

    <!-- 抑制未使用方法的警告（对于API方法） -->
    <issue id="UnusedMethod" severity="ignore">
        <ignore path="**/ConfigManager.java" />
    </issue>

    <!-- 抑制未使用参数的警告 -->
    <issue id="UnusedParameter" severity="ignore" />
</lint>
