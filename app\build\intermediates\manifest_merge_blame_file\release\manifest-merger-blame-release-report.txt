1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.floatingfeishu"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 必须权限 -->
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:6:5-78
12-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:7:5-77
13-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:7:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:8:5-87
14-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:8:22-84
15
16    <!-- 可选权限 -->
17    <uses-permission android:name="android.permission.INTERNET" />
17-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:11:5-67
17-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:11:22-64
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:12:5-79
18-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:12:22-76
19
20    <permission
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
21        android:name="com.example.floatingfeishu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.example.floatingfeishu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
25
26    <application
26-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:14:5-37:19
27        android:allowBackup="true"
27-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:15:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
29        android:extractNativeLibs="true"
30        android:icon="@mipmap/ic_launcher"
30-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:16:9-43
31        android:label="@string/app_name"
31-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:17:9-41
32        android:roundIcon="@mipmap/ic_launcher_round"
32-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:18:9-54
33        android:supportsRtl="true"
33-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:19:9-35
34        android:theme="@style/Theme.FloatingFeishu" >
34-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:20:9-52
35        <service
35-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:22:9-27:19
36            android:name="com.example.floatingfeishu.FloatingBallService"
36-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:23:13-48
37            android:enabled="true"
37-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:24:13-35
38            android:exported="false"
38-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:25:13-37
39            android:foregroundServiceType="dataSync" >
39-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:26:13-53
40        </service>
41
42        <activity
42-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:29:9-36:20
43            android:name="com.example.floatingfeishu.MainActivity"
43-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:30:13-41
44            android:exported="true" >
44-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:31:13-36
45            <intent-filter>
45-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:32:13-35:29
46                <action android:name="android.intent.action.MAIN" />
46-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:33:17-69
46-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:33:25-66
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:34:17-77
48-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:34:27-74
49            </intent-filter>
50        </activity>
51
52        <provider
52-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
53            android:name="androidx.startup.InitializationProvider"
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
54            android:authorities="com.example.floatingfeishu.androidx-startup"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
55            android:exported="false" >
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.emoji2.text.EmojiCompatInitializer"
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
58                android:value="androidx.startup" />
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\914a02d4ef33b4a2d3f9bc481d83e7fc\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\914a02d4ef33b4a2d3f9bc481d83e7fc\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
61                android:value="androidx.startup" />
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\914a02d4ef33b4a2d3f9bc481d83e7fc\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
64                android:value="androidx.startup" />
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
65        </provider>
66
67        <receiver
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
68            android:name="androidx.profileinstaller.ProfileInstallReceiver"
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
69            android:directBootAware="false"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
70            android:enabled="true"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
71            android:exported="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
72            android:permission="android.permission.DUMP" >
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
74                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
77                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
80                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
83                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
84            </intent-filter>
85        </receiver>
86    </application>
87
88</manifest>
