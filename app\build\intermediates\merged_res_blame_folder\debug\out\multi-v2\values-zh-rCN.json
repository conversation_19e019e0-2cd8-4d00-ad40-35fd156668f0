{"logs": [{"outputFile": "com.example.floatingfeishu.app-mergeDebugResources-30:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cf89ab1ea7cc19352d4f47103c74320d\\transformed\\core-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "7688", "endColumns": "100", "endOffsets": "7784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecca92267eba45ce3e43e495f0e1965d\\transformed\\material-1.10.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,922,999,1058,1117,1195,1256,1313,1369,1428,1486,1540,1625,1681,1739,1793,1858,1950,2024,2100,2222,2284,2346,2445,2524,2598,2648,2699,2765,2829,2898,2976,3047,3108,3179,3246,3306,3392,3471,3538,3621,3706,3780,3845,3921,3969,4042,4106,4182,4260,4322,4386,4449,4514,4594,4670,4748,4824,4878,4933", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "242,306,368,438,508,585,676,782,855,917,994,1053,1112,1190,1251,1308,1364,1423,1481,1535,1620,1676,1734,1788,1853,1945,2019,2095,2217,2279,2341,2440,2519,2593,2643,2694,2760,2824,2893,2971,3042,3103,3174,3241,3301,3387,3466,3533,3616,3701,3775,3840,3916,3964,4037,4101,4177,4255,4317,4381,4444,4509,4589,4665,4743,4819,4873,4928,4997"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2854,2918,2980,3050,3120,3197,3288,3394,3467,3529,3606,3665,3724,3802,3863,3920,3976,4035,4093,4147,4232,4288,4346,4400,4465,4557,4631,4707,4829,4891,4953,5052,5131,5205,5255,5306,5372,5436,5505,5583,5654,5715,5786,5853,5913,5999,6078,6145,6228,6313,6387,6452,6528,6576,6649,6713,6789,6867,6929,6993,7056,7121,7201,7277,7355,7431,7485,7540", "endLines": "5,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "292,2913,2975,3045,3115,3192,3283,3389,3462,3524,3601,3660,3719,3797,3858,3915,3971,4030,4088,4142,4227,4283,4341,4395,4460,4552,4626,4702,4824,4886,4948,5047,5126,5200,5250,5301,5367,5431,5500,5578,5649,5710,5781,5848,5908,5994,6073,6140,6223,6308,6382,6447,6523,6571,6644,6708,6784,6862,6924,6988,7051,7116,7196,7272,7350,7426,7480,7535,7604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38fdf4e56534b3c4c6a8297bf7eeddee\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,7609", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,7683"}}]}]}