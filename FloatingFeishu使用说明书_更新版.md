# FloatingFeishu 悬浮飞书应用使用说明书

## 基础介绍

FloatingFeishu是一款悬浮窗应用，可以让您在任何界面快速发送消息到飞书群组，无需切换应用。非常适合需要频繁发送通知或提醒的场景。

## 安装指南

1. **下载安装包**
   - 您可以使用以下文件进行安装：
   - 路径：`app/build/outputs/apk/debug/app-debug.apk`

2. **首次安装注意事项**
   - 安装时可能会提示"未知来源应用"，请点击"设置"并允许来自此来源的安装
   - 如果您使用的是Android 8.0及以上版本，需要在设置中允许安装未知来源应用

## 首次使用设置

### 第一步：授予悬浮窗权限

1. 打开FloatingFeishu应用
2. 应用会检查悬浮窗权限，如果没有权限，会自动跳转到权限设置页面
3. 在打开的设置页面中，找到"显示在其他应用上层"或"悬浮窗"选项
4. 将FloatingFeishu的开关打开
5. 返回应用，应用会自动加载配置界面

### 第二步：配置飞书Webhook

1. 在应用主界面，您会看到两个输入框：
   - **Webhook URL输入框**：需要填入飞书机器人的Webhook地址
   - **默认消息输入框**：设置快速发送的默认消息内容

2. **获取飞书Webhook URL的两种方法**：

   **方法一：传统手动配置（当前版本）**
   - 在飞书电脑客户端中，进入您想要接收消息的群组
   - 点击群组右上角的设置图标
   - 选择"群机器人"
   - 点击"添加机器人"，选择"自定义机器人"
   - 设置机器人名称和头像
   - 复制生成的Webhook URL
   - 将此URL粘贴到应用的Webhook URL输入框中

   **方法二：应用机器人自动配置（推荐，即将推出）**
   - 创建应用机器人：
     * 在开发者后台创建应用并开启机器人能力
     * 订阅所需事件（如消息接收事件）
   - 配置事件订阅：
     * 在应用后台设置 Webhook 地址接收事件
     * 当触发事件时，飞书会向你的服务器推送数据，可解析后保存
   - 优势：
     * 自动获取群组信息和权限
     * 支持更丰富的交互功能
     * 更安全的认证机制
     * 可以接收群组变更通知

3. 填写完成后，点击"保存配置"按钮

## 应用机器人模式配置（推荐）

### 什么是应用机器人模式？

应用机器人模式是一种更安全、更强大的飞书集成方式。与传统的Webhook模式相比，它提供：
- 更高的安全性（OAuth 2.0认证）
- 更丰富的功能（支持多种消息类型）
- 更好的稳定性（官方API支持）
- 精细的权限管理

### 配置步骤

#### 第一步：创建飞书应用
1. 访问 [飞书开发者后台](https://open.feishu.cn/app)
2. 点击"创建应用" → "自建应用"
3. 填写应用信息：
   - 应用名称：FloatingFeishu
   - 应用描述：悬浮飞书消息发送工具
4. 记录生成的 **App ID** 和 **App Secret**

#### 第二步：配置应用权限
1. 在应用管理页面，启用"机器人"功能
2. 在"权限管理"中申请以下权限：
   - `im:message` - 发送消息
   - `im:message:send_as_bot` - 以机器人身份发送
3. 等待管理员审批权限（如果需要）

#### 第三步：获取群组ID
1. 将机器人添加到目标群组
2. 在群组设置中查看群组ID，或
3. 使用飞书API获取群组列表

#### 第四步：在应用中配置
1. 打开FloatingFeishu应用
2. 选择"应用机器人模式"
3. 填写配置信息：
   - **应用ID**：步骤一中获取的App ID
   - **应用密钥**：步骤一中获取的App Secret
   - **群组ID**：目标群组的Chat ID
   - **默认消息**：快速发送的消息内容
4. 点击"测试发送"验证配置
5. 测试成功后，点击"保存配置"

### 详细配置指南

如需更详细的配置说明，请参考项目中的《应用机器人配置指南.md》文件。

## 日常使用方法

### 悬浮球特性（类似苹果悬浮球）

1. **自动吸附**：悬浮球会自动吸附到屏幕边缘，不会挡住中间内容
2. **半透明效果**：悬浮球采用半透明设计，不会过于显眼
3. **点击展开**：点击悬浮球会显示发送按钮
4. **自动收起**：3秒内无操作会自动收起发送按钮
5. **拖动定位**：可以上下拖动调整悬浮球位置

### 启动悬浮球

1. 打开FloatingFeishu应用
2. 应用会自动启动悬浮球并显示在屏幕上
3. 您可以按Home键或切换到其他应用，悬浮球会保持显示
4. 悬浮球会自动吸附到屏幕边缘

### 移动悬浮球

1. 用手指按住悬浮球
2. 拖动到屏幕上您喜欢的位置
3. 松开手指，悬浮球会自动吸附到最近的屏幕边缘

### 发送消息

1. 点击悬浮球，会展开并显示发送按钮
2. 点击发送按钮，应用会立即发送您预设的默认消息到飞书群组
3. 发送成功后，会显示"消息已发送"提示
4. 发送后悬浮球会自动收起

### 修改配置

如需修改Webhook URL或默认消息：

1. 点击通知栏中的"飞书悬浮球"通知，或重新打开应用
2. 修改相应的输入框内容
3. 点击"保存配置"按钮

## 常见问题解答

### 问题1：悬浮球不显示或消失

**解决方法**：
- 检查是否授予了悬浮窗权限
- 点击通知栏中的"飞书悬浮球"通知重新打开
- 重启应用

### 问题2：消息发送失败

**解决方法**：
- 检查网络连接是否正常
- 验证Webhook URL是否正确
- 确认飞书群组和机器人仍然有效

### 问题3：悬浮球影响其他应用操作

**解决方法**：
- 悬浮球会自动吸附到屏幕边缘，只露出一小部分
- 如果仍有影响，可以暂时关闭应用

## 新版特性

### 界面和交互改进
1. **改进的UI设计**：更美观的圆形悬浮球，采用渐变色背景
2. **边缘吸附**：自动吸附到屏幕边缘，不会挡住中间内容
3. **平滑动画**：添加了展开/收起的平滑动画效果
4. **自动收起**：3秒无操作自动收起，不干扰正常使用
5. **前台服务**：通过前台服务提高稳定性，防止被系统杀死

### 功能增强
6. **双模式支持**：支持传统Webhook模式和新的应用机器人模式
7. **配置界面优化**：全新的卡片式配置界面，更直观易用
8. **测试功能**：新增测试发送功能，可以在保存前验证配置
9. **智能切换**：根据选择的模式自动显示相应的配置选项

### 应用机器人模式优势
10. **更高安全性**：使用OAuth 2.0认证，无需暴露敏感URL
11. **更强功能**：支持更丰富的消息类型和交互功能
12. **更好稳定性**：官方API支持，连接更稳定可靠
13. **权限管理**：精细的权限控制，符合企业安全要求

## 技术规格

- 最低支持Android版本：Android 5.0 (Lollipop)
- 推荐Android版本：Android 8.0及以上
- 应用大小：约5MB
- 权限需求：
  - 悬浮窗权限（SYSTEM_ALERT_WINDOW）
  - 网络访问权限（INTERNET）

---

**注意**：本应用仅用于便捷发送消息，请勿用于发送敏感信息或违反相关法律法规的内容。
