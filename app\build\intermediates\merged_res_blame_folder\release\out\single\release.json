[{"merged": "com.example.floatingfeishu.app-merged_res-32:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/layout_simple_floating_ball.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/layout/simple_floating_ball.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/drawable_bg_floating_ball.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/drawable/bg_floating_ball.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/layout_floating_ball.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/layout/floating_ball.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/layout_activity_main.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/layout/activity_main.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/layout_activity_main_enhanced.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/layout/activity_main_enhanced.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.floatingfeishu.app-merged_res-32:/layout_layout_floating_ball.xml.flat", "source": "com.example.floatingfeishu.app-main-33:/layout/layout_floating_ball.xml"}]