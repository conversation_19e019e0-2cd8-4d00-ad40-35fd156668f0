<lint-module
    format="1"
    dir="E:\FloatingFeishu\app"
    name=":app"
    type="APP"
    maven="FloatingFeishu:app:"
    agpVersion="8.1.0"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\33.0.1\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      lintConfig="lint.xml"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
