E:\FloatingFeishu\app\lint-baseline.xml: Information: 2 errors/warnings were listed in the baseline file (lint-baseline.xml) but not found in the project; perhaps they have been fixed? Another possible explanation is that lint recently stopped analyzing (and including results from) dependent projects by default. You can turn this back on with android.lintOptions.checkDependencies=true. Unmatched issue types: UnknownIssueId (2) [LintBaseline]
E:\FloatingFeishu\app\lint-baseline.xml: Information: 23 warnings were filtered out because they are listed in the baseline file, lint-baseline.xml
 [LintBaseline]
0 errors, 0 warnings (23 warnings filtered by baseline lint-baseline.xml)
