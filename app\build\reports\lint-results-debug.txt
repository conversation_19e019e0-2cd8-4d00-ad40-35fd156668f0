E:\FloatingFeishu\app\build.gradle:12: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTarget<PERSON><PERSON>]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

E:\FloatingFeishu\app\src\main\java\com\example\floatingfeishu\FloatingBallService.java:99: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
            floatingView = LayoutInflater.from(this).inflate(R.layout.simple_floating_ball, null);
                                                                                            ~~~~
E:\FloatingFeishu\app\src\main\java\com\example\floatingfeishu\FloatingBallService.java:104: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
                floatingView = LayoutInflater.from(this).inflate(R.layout.layout_floating_ball, null);
                                                                                                ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:277: Warning: Use SwitchCompat from AppCompat or SwitchMaterial from Material library [UseSwitchCompatOrMaterialXml from androidx.appcompat]
                    <Switch
                    ^

   Explanation for issues of type "UseSwitchCompatOrMaterialXml":
   Use SwitchCompat from AppCompat or SwitchMaterial from Material library

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:6: Warning: Possible overdraw: Root element paints background #f5f5f5 with a theme that also paints a background (inferred theme is @style/Theme.FloatingFeishu) [Overdraw]
    android:background="#f5f5f5">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:7: Warning: Possible overdraw: Root element paints background #F5F5F5 with a theme that also paints a background (inferred theme is @style/Theme.FloatingFeishu) [Overdraw]
    android:background="#F5F5F5">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:2: Warning: The resource R.layout.activity_main_enhanced appears to be unused [UnusedResources]
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
^
E:\FloatingFeishu\app\src\main\res\layout\floating_ball.xml:2: Warning: The resource R.layout.floating_ball appears to be unused [UnusedResources]
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

E:\FloatingFeishu\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml:2: Warning: The application adaptive icon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^
E:\FloatingFeishu\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml:2: Warning: The application adaptive roundIcon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "MonochromeLauncherIcon":
   If android:roundIcon and android:icon are both in your manifest, you must
   either remove the reference to android:roundIcon if it is not needed; or,
   supply the monochrome icon in the drawable defined by the android:roundIcon
   and android:icon attribute.

   For example, if android:roundIcon and android:icon are both in the
   manifest, a launcher might choose to use android:roundIcon over
   android:icon to display the adaptive app icon. Therefore, your themed
   application iconwill not show if your monochrome attribute is not also
   specified in android:roundIcon.

E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:345: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:357: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://material.io/components/dialogs/

E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:53: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:70: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:135: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:152: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:108: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:154: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:171: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:188: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:232: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:298: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:315: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

E:\FloatingFeishu\app\src\main\java\com\example\floatingfeishu\FloatingBallService.java:161: Warning: Custom view `ImageView` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        floatingBall.setOnTouchListener(new View.OnTouchListener() {
        ^
E:\FloatingFeishu\app\src\main\java\com\example\floatingfeishu\FloatingBallService.java:171: Warning: onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
            public boolean onTouch(View v, MotionEvent event) {
                           ~~~~~~~

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:18: Warning: Hardcoded string "FloatingFeishu 自动化配置", should use @string resource [HardcodedText]
        android:text="FloatingFeishu 自动化配置"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:41: Warning: Hardcoded string "基础配置", should use @string resource [HardcodedText]
                android:text="基础配置"
                ~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:49: Warning: Hardcoded string "Webhook URL:", should use @string resource [HardcodedText]
                android:text="Webhook URL:"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:57: Warning: Hardcoded string "请输入飞书机器人的Webhook URL", should use @string resource [HardcodedText]
                android:hint="请输入飞书机器人的Webhook URL"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:66: Warning: Hardcoded string "默认消息:", should use @string resource [HardcodedText]
                android:text="默认消息:"
                ~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:74: Warning: Hardcoded string "请输入默认发送的消息内容", should use @string resource [HardcodedText]
                android:hint="请输入默认发送的消息内容"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:85: Warning: Hardcoded string "保存基础配置", should use @string resource [HardcodedText]
                android:text="保存基础配置"
                ~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:93: Warning: Hardcoded string "测试发送消息", should use @string resource [HardcodedText]
                android:text="测试发送消息"
                ~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:116: Warning: Hardcoded string "自动化流程配置", should use @string resource [HardcodedText]
                android:text="自动化流程配置"
                ~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:125: Warning: Hardcoded string "启用自动化流程", should use @string resource [HardcodedText]
                android:text="启用自动化流程"
                ~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:131: Warning: Hardcoded string "自动化Webhook URL:", should use @string resource [HardcodedText]
                android:text="自动化Webhook URL:"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:139: Warning: Hardcoded string "请输入飞书自动化流程的Webhook URL", should use @string resource [HardcodedText]
                android:hint="请输入飞书自动化流程的Webhook URL"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:148: Warning: Hardcoded string "验证密钥 (可选):", should use @string resource [HardcodedText]
                android:text="验证密钥 (可选):"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:156: Warning: Hardcoded string "请输入验证密钥（用于签名验证）", should use @string resource [HardcodedText]
                android:hint="请输入验证密钥（用于签名验证）"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:166: Warning: Hardcoded string "保存自动化配置", should use @string resource [HardcodedText]
                android:text="保存自动化配置"
                ~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:174: Warning: Hardcoded string "测试自动化连接", should use @string resource [HardcodedText]
                android:text="测试自动化连接"
                ~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:196: Warning: Hardcoded string "使用说明", should use @string resource [HardcodedText]
                android:text="使用说明"
                ~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:204: Warning: Hardcoded string "1. 配置基础的Webhook URL和默认消息", should use @string resource [HardcodedText]
                android:text="1. 配置基础的Webhook URL和默认消息"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:210: Warning: Hardcoded string "2. 启用自动化流程，配置自动化Webhook URL", should use @string resource [HardcodedText]
                android:text="2. 启用自动化流程，配置自动化Webhook URL"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:216: Warning: Hardcoded string "3. 测试各项连接确保配置正确", should use @string resource [HardcodedText]
                android:text="3. 测试各项连接确保配置正确"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:222: Warning: Hardcoded string "4. 悬浮球会显示在屏幕上", should use @string resource [HardcodedText]
                android:text="4. 悬浮球会显示在屏幕上"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml:228: Warning: Hardcoded string "5. 使用悬浮球发送消息，系统将自动记录到多维表格", should use @string resource [HardcodedText]
                android:text="5. 使用悬浮球发送消息，系统将自动记录到多维表格"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:18: Warning: Hardcoded string "FloatingFeishu 配置", should use @string resource [HardcodedText]
            android:text="FloatingFeishu 配置"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:42: Warning: Hardcoded string "配置模式", should use @string resource [HardcodedText]
                    android:text="配置模式"
                    ~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:58: Warning: Hardcoded string "Webhook模式（传统方式）", should use @string resource [HardcodedText]
                        android:text="Webhook模式（传统方式）"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:67: Warning: Hardcoded string "应用机器人模式（推荐）", should use @string resource [HardcodedText]
                        android:text="应用机器人模式（推荐）"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:95: Warning: Hardcoded string "Webhook配置", should use @string resource [HardcodedText]
                    android:text="Webhook配置"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:104: Warning: Hardcoded string "Webhook URL:", should use @string resource [HardcodedText]
                    android:text="Webhook URL:"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:112: Warning: Hardcoded string "请输入飞书机器人的Webhook URL", should use @string resource [HardcodedText]
                    android:hint="请输入飞书机器人的Webhook URL"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:141: Warning: Hardcoded string "应用机器人配置", should use @string resource [HardcodedText]
                    android:text="应用机器人配置"
                    ~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:150: Warning: Hardcoded string "应用ID (App ID):", should use @string resource [HardcodedText]
                    android:text="应用ID (App ID):"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:158: Warning: Hardcoded string "请输入应用ID", should use @string resource [HardcodedText]
                    android:hint="请输入应用ID"
                    ~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:167: Warning: Hardcoded string "应用密钥 (App Secret):", should use @string resource [HardcodedText]
                    android:text="应用密钥 (App Secret):"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:175: Warning: Hardcoded string "请输入应用密钥", should use @string resource [HardcodedText]
                    android:hint="请输入应用密钥"
                    ~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:184: Warning: Hardcoded string "群组ID (Chat ID):", should use @string resource [HardcodedText]
                    android:text="群组ID (Chat ID):"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:192: Warning: Hardcoded string "请输入群组ID", should use @string resource [HardcodedText]
                    android:hint="请输入群组ID"
                    ~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:219: Warning: Hardcoded string "消息配置", should use @string resource [HardcodedText]
                    android:text="消息配置"
                    ~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:228: Warning: Hardcoded string "默认消息:", should use @string resource [HardcodedText]
                    android:text="默认消息:"
                    ~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:236: Warning: Hardcoded string "请输入默认发送的消息内容", should use @string resource [HardcodedText]
                    android:hint="请输入默认发送的消息内容"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:272: Warning: Hardcoded string "多维表格记录", should use @string resource [HardcodedText]
                        android:text="多维表格记录"
                        ~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:294: Warning: Hardcoded string "多维表格App Token:", should use @string resource [HardcodedText]
                        android:text="多维表格App Token:"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:302: Warning: Hardcoded string "请输入多维表格的App Token", should use @string resource [HardcodedText]
                        android:hint="请输入多维表格的App Token"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:311: Warning: Hardcoded string "数据表Table ID:", should use @string resource [HardcodedText]
                        android:text="数据表Table ID:"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:319: Warning: Hardcoded string "请输入数据表的Table ID", should use @string resource [HardcodedText]
                        android:hint="请输入数据表的Table ID"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:350: Warning: Hardcoded string "保存配置", should use @string resource [HardcodedText]
                    android:text="保存配置"
                    ~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:362: Warning: Hardcoded string "测试发送", should use @string resource [HardcodedText]
                    android:text="测试发送"
                    ~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml:376: Warning: Hardcoded string "测试多维表格连接", should use @string resource [HardcodedText]
                android:text="测试多维表格连接"
                ~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\floating_ball.xml:24: Warning: Hardcoded string "Floating Ball", should use @string resource [HardcodedText]
            android:contentDescription="Floating Ball" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\floating_ball.xml:32: Warning: Hardcoded string "发送", should use @string resource [HardcodedText]
        android:text="发送"
        ~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\layout_floating_ball.xml:24: Warning: Hardcoded string "Floating Ball", should use @string resource [HardcodedText]
            android:contentDescription="Floating Ball" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\layout_floating_ball.xml:32: Warning: Hardcoded string "发送", should use @string resource [HardcodedText]
        android:text="发送"
        ~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\simple_floating_ball.xml:23: Warning: Hardcoded string "Floating Ball", should use @string resource [HardcodedText]
            android:contentDescription="Floating Ball" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
E:\FloatingFeishu\app\src\main\res\layout\simple_floating_ball.xml:31: Warning: Hardcoded string "发送", should use @string resource [HardcodedText]
        android:text="发送"
        ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

0 errors, 78 warnings
