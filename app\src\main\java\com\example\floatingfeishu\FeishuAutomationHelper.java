package com.example.floatingfeishu;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import org.json.JSONObject;

import java.io.IOException;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 飞书自动化助手类
 * 负责与飞书多维表格自动化流程和机器人助手的集成
 */
public class FeishuAutomationHelper {

    private static final String TAG = "FeishuAutomationHelper";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private static FeishuAutomationHelper instance;
    private final OkHttpClient client;
    private final Context context;

    private FeishuAutomationHelper(Context context) {
        this.context = context.getApplicationContext();
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
    }

    public static synchronized FeishuAutomationHelper getInstance(Context context) {
        if (instance == null) {
            instance = new FeishuAutomationHelper(context);
        }
        return instance;
    }

    /**
     * 触发多维表格自动化流程
     * 通过Webhook触发器向多维表格发送数据
     */
    public void triggerBitableAutomation(String message, String mode, String result) {
        ConfigManager configManager = ConfigManager.getInstance(context);
        
        if (!configManager.isAutomationEnabled()) {
            Log.d(TAG, "自动化流程未启用");
            return;
        }

        String webhookUrl = configManager.getAutomationWebhookUrl();
        if (webhookUrl == null || webhookUrl.isEmpty()) {
            Log.w(TAG, "自动化Webhook URL未配置");
            return;
        }

        new Thread(() -> {
            try {
                JSONObject payload = buildAutomationPayload(message, mode, result);
                sendAutomationWebhook(webhookUrl, payload);
            } catch (Exception e) {
                Log.e(TAG, "触发自动化流程失败", e);
            }
        }).start();
    }

    /**
     * 构建自动化流程的数据载荷
     */
    private JSONObject buildAutomationPayload(String message, String mode, String result) {
        JSONObject payload = new JSONObject();
        try {
            // 基本信息
            payload.put("timestamp", System.currentTimeMillis());
            payload.put("datetime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date()));
            payload.put("message", message);
            payload.put("mode", mode);
            payload.put("result", result);
            
            // 设备信息
            payload.put("device_model", Build.MODEL);
            payload.put("device_brand", Build.BRAND);
            payload.put("android_version", Build.VERSION.RELEASE);
            payload.put("app_version", "1.0.0");
            
            // 添加签名验证（如果配置了密钥）
            ConfigManager configManager = ConfigManager.getInstance(context);
            String secret = configManager.getAutomationSecret();
            if (secret != null && !secret.isEmpty()) {
                String signature = generateSignature(payload.toString(), secret);
                payload.put("signature", signature);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "构建自动化载荷失败", e);
        }
        return payload;
    }

    /**
     * 发送自动化Webhook请求
     */
    private void sendAutomationWebhook(String webhookUrl, JSONObject payload) {
        try {
            RequestBody body = RequestBody.create(payload.toString(), JSON);
            Request request = new Request.Builder()
                    .url(webhookUrl)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("User-Agent", "FloatingFeishu/1.0")
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "自动化流程触发成功: " + response.code());
                    String responseBody = response.body() != null ? response.body().string() : "";
                    Log.d(TAG, "响应内容: " + responseBody);
                } else {
                    Log.e(TAG, "自动化流程触发失败: " + response.code() + " - " + 
                          (response.body() != null ? response.body().string() : ""));
                }
            }
        } catch (IOException e) {
            Log.e(TAG, "发送自动化Webhook请求失败", e);
        }
    }

    /**
     * 生成签名用于验证请求
     */
    private String generateSignature(String data, String secret) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            String input = data + secret;
            byte[] hash = md.digest(input.getBytes("UTF-8"));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            Log.e(TAG, "生成签名失败", e);
            return "";
        }
    }

    /**
     * 直接向多维表格添加记录
     * 使用飞书开放平台API
     */
    public void addBitableRecord(String message, String mode, String result) {
        ConfigManager configManager = ConfigManager.getInstance(context);
        
        if (!configManager.isBitableEnabled()) {
            Log.d(TAG, "多维表格集成未启用");
            return;
        }

        String appToken = configManager.getBitableAppToken();
        String tableId = configManager.getBitableTableId();
        
        if (appToken == null || tableId == null) {
            Log.w(TAG, "多维表格配置不完整");
            return;
        }

        new Thread(() -> {
            try {
                // 首先获取访问令牌
                String accessToken = getAccessToken();
                if (accessToken != null) {
                    JSONObject record = buildBitableRecord(message, mode, result);
                    addRecordToBitable(accessToken, appToken, tableId, record);
                }
            } catch (Exception e) {
                Log.e(TAG, "添加多维表格记录失败", e);
            }
        }).start();
    }

    /**
     * 获取访问令牌
     */
    private String getAccessToken() {
        ConfigManager configManager = ConfigManager.getInstance(context);
        String appId = configManager.getAppId();
        String appSecret = configManager.getAppSecret();
        
        if (appId == null || appSecret == null) {
            Log.w(TAG, "应用凭证未配置");
            return null;
        }

        try {
            JSONObject requestBody = new JSONObject();
            requestBody.put("app_id", appId);
            requestBody.put("app_secret", appSecret);

            RequestBody body = RequestBody.create(requestBody.toString(), JSON);
            Request request = new Request.Builder()
                    .url("https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal")
                    .post(body)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseStr = response.body().string();
                    JSONObject responseJson = new JSONObject(responseStr);
                    
                    if (responseJson.getInt("code") == 0) {
                        return responseJson.getString("tenant_access_token");
                    } else {
                        Log.e(TAG, "获取访问令牌失败: " + responseJson.getString("msg"));
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取访问令牌异常", e);
        }
        
        return null;
    }

    /**
     * 构建多维表格记录
     */
    private JSONObject buildBitableRecord(String message, String mode, String result) {
        ConfigManager configManager = ConfigManager.getInstance(context);
        JSONObject record = new JSONObject();
        
        try {
            JSONObject fields = new JSONObject();
            
            // 使用配置的字段名称
            fields.put(configManager.getBitableTimeField(), System.currentTimeMillis());
            fields.put(configManager.getBitableMessageField(), message);
            fields.put(configManager.getBitableModeField(), mode);
            fields.put(configManager.getBitableResultField(), result);
            fields.put(configManager.getBitableDeviceField(), Build.MODEL + " (Android " + Build.VERSION.RELEASE + ")");
            fields.put(configManager.getBitableNoteField(), "通过FloatingFeishu悬浮球发送");
            
            record.put("fields", fields);
        } catch (Exception e) {
            Log.e(TAG, "构建多维表格记录失败", e);
        }
        
        return record;
    }

    /**
     * 向多维表格添加记录
     */
    private void addRecordToBitable(String accessToken, String appToken, String tableId, JSONObject record) {
        try {
            String url = String.format("https://open.feishu.cn/open-apis/bitable/v1/apps/%s/tables/%s/records", 
                                     appToken, tableId);
            
            RequestBody body = RequestBody.create(record.toString(), JSON);
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Authorization", "Bearer " + accessToken)
                    .addHeader("Content-Type", "application/json")
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    Log.i(TAG, "多维表格记录添加成功");
                } else {
                    Log.e(TAG, "多维表格记录添加失败: " + response.code() + " - " + 
                          (response.body() != null ? response.body().string() : ""));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "添加多维表格记录异常", e);
        }
    }

    /**
     * 测试自动化流程连接
     */
    public void testAutomationConnection(TestCallback callback) {
        new Thread(() -> {
            try {
                triggerBitableAutomation("测试消息", "自动化测试", "测试成功");
                if (callback != null) {
                    callback.onSuccess("自动化流程测试成功");
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError("自动化流程测试失败: " + e.getMessage());
                }
            }
        }).start();
    }

    /**
     * 测试回调接口
     */
    public interface TestCallback {
        void onSuccess(String message);
        void onError(String error);
    }
}
