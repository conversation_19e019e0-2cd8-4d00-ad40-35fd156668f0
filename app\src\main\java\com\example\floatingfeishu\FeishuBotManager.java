package com.example.floatingfeishu;

import android.content.Context;
import android.util.Log;
import okhttp3.*;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 飞书应用机器人管理器
 * 负责处理应用机器人的认证、事件订阅和消息发送
 */
public class FeishuBotManager {
    
    private static final String TAG = "FeishuBotManager";
    private static FeishuBotManager instance;
    
    // 飞书开放平台API地址
    private static final String FEISHU_API_BASE = "https://open.feishu.cn/open-apis";
    private static final String TOKEN_URL = FEISHU_API_BASE + "/auth/v3/tenant_access_token/internal";
    private static final String SEND_MESSAGE_URL = FEISHU_API_BASE + "/im/v1/messages";
    
    private final OkHttpClient client;
    private final ConfigManager configManager;
    
    // 应用凭证（需要从飞书开发者后台获取）
    private String appId;
    private String appSecret;
    private String tenantAccessToken;
    private long tokenExpireTime;
    
    private FeishuBotManager(Context context) {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
        this.configManager = ConfigManager.getInstance(context);
        
        // 从配置中加载应用凭证
        loadAppCredentials();
    }
    
    public static synchronized FeishuBotManager getInstance(Context context) {
        if (instance == null) {
            instance = new FeishuBotManager(context);
        }
        return instance;
    }
    
    /**
     * 设置应用凭证
     */
    public void setAppCredentials(String appId, String appSecret) {
        this.appId = appId;
        this.appSecret = appSecret;
        saveAppCredentials();
    }
    
    /**
     * 从配置中加载应用凭证
     */
    private void loadAppCredentials() {
        // 这里可以从SharedPreferences或其他安全存储中加载
        // 暂时使用硬编码，实际使用时应该从配置文件或用户输入获取
        this.appId = configManager.getAppId();
        this.appSecret = configManager.getAppSecret();
    }
    
    /**
     * 保存应用凭证
     */
    private void saveAppCredentials() {
        configManager.saveAppCredentials(appId, appSecret);
    }
    
    /**
     * 获取租户访问令牌
     */
    public void getTenantAccessToken(TokenCallback callback) {
        if (appId == null || appSecret == null) {
            callback.onError("应用凭证未配置");
            return;
        }
        
        // 检查token是否还有效
        if (tenantAccessToken != null && System.currentTimeMillis() < tokenExpireTime) {
            callback.onSuccess(tenantAccessToken);
            return;
        }
        
        new Thread(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("app_id", appId);
                requestBody.put("app_secret", appSecret);
                
                RequestBody body = RequestBody.create(
                    requestBody.toString(),
                    MediaType.get("application/json; charset=utf-8")
                );
                
                Request request = new Request.Builder()
                        .url(TOKEN_URL)
                        .post(body)
                        .build();
                
                try (Response response = client.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseStr = response.body().string();
                        JSONObject responseJson = new JSONObject(responseStr);
                        
                        if (responseJson.getInt("code") == 0) {
                            tenantAccessToken = responseJson.getString("tenant_access_token");
                            int expiresIn = responseJson.getInt("expire");
                            tokenExpireTime = System.currentTimeMillis() + (expiresIn - 60) * 1000L; // 提前1分钟过期
                            
                            callback.onSuccess(tenantAccessToken);
                        } else {
                            callback.onError("获取token失败: " + responseJson.getString("msg"));
                        }
                    } else {
                        callback.onError("请求失败: " + response.code());
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "获取租户访问令牌失败", e);
                callback.onError("获取token异常: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * 发送消息到指定群组
     */
    public void sendMessageToGroup(String chatId, String content, boolean mentionAll, MessageCallback callback) {
        getTenantAccessToken(new TokenCallback() {
            @Override
            public void onSuccess(String token) {
                sendMessageWithToken(token, chatId, content, mentionAll, callback);
            }
            
            @Override
            public void onError(String error) {
                callback.onError("获取token失败: " + error);
            }
        });
    }
    
    /**
     * 使用token发送消息
     */
    private void sendMessageWithToken(String token, String chatId, String content, boolean mentionAll, MessageCallback callback) {
        new Thread(() -> {
            try {
                JSONObject message = buildMessage(content, mentionAll);
                
                RequestBody body = RequestBody.create(
                    message.toString(),
                    MediaType.get("application/json; charset=utf-8")
                );
                
                String url = SEND_MESSAGE_URL + "?receive_id_type=chat_id";
                
                Request request = new Request.Builder()
                        .url(url)
                        .addHeader("Authorization", "Bearer " + token)
                        .addHeader("Content-Type", "application/json")
                        .post(body)
                        .build();
                
                try (Response response = client.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseStr = response.body().string();
                        JSONObject responseJson = new JSONObject(responseStr);
                        
                        if (responseJson.getInt("code") == 0) {
                            callback.onSuccess("消息发送成功");
                        } else {
                            callback.onError("发送失败: " + responseJson.getString("msg"));
                        }
                    } else {
                        callback.onError("请求失败: " + response.code());
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "发送消息失败", e);
                callback.onError("发送异常: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * 构建消息体
     */
    private JSONObject buildMessage(String content, boolean mentionAll) throws JSONException {
        JSONObject message = new JSONObject();
        message.put("msg_type", "text");
        
        JSONObject contentObj = new JSONObject();
        String text = mentionAll ? "<at user_id=\"all\">所有人</at> " + content : content;
        contentObj.put("text", text);
        
        message.put("content", contentObj);
        return message;
    }
    
    /**
     * Token获取回调接口
     */
    public interface TokenCallback {
        void onSuccess(String token);
        void onError(String error);
    }
    
    /**
     * 消息发送回调接口
     */
    public interface MessageCallback {
        void onSuccess(String result);
        void onError(String error);
    }
}
