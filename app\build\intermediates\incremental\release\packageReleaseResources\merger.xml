<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\FloatingFeishu\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\FloatingFeishu\app\src\main\res"><file name="bg_floating_ball" path="E:\FloatingFeishu\app\src\main\res\drawable\bg_floating_ball.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\FloatingFeishu\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="E:\FloatingFeishu\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_main_enhanced" path="E:\FloatingFeishu\app\src\main\res\layout\activity_main_enhanced.xml" qualifiers="" type="layout"/><file name="floating_ball" path="E:\FloatingFeishu\app\src\main\res\layout\floating_ball.xml" qualifiers="" type="layout"/><file name="layout_floating_ball" path="E:\FloatingFeishu\app\src\main\res\layout\layout_floating_ball.xml" qualifiers="" type="layout"/><file name="simple_floating_ball" path="E:\FloatingFeishu\app\src\main\res\layout\simple_floating_ball.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\FloatingFeishu\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="E:\FloatingFeishu\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="E:\FloatingFeishu\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#6200EE</color><color name="colorPrimaryDark">#3700B3</color><color name="colorAccent">#03DAC5</color></file><file path="E:\FloatingFeishu\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#3DDC84</color></file><file path="E:\FloatingFeishu\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">FloatingFeishu</string></file><file path="E:\FloatingFeishu\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.FloatingFeishu" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\FloatingFeishu\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\FloatingFeishu\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\FloatingFeishu\app\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\FloatingFeishu\app\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>