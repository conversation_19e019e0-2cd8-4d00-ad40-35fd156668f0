<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!-- 简化的悬浮球 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/ball_container"
        android:layout_width="60dp"
        android:layout_height="60dp"
        app:cardCornerRadius="30dp"
        app:cardElevation="4dp"
        android:alpha="0.9">

        <ImageView
            android:id="@+id/floating_ball"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@android:drawable/ic_menu_share"
            android:background="@android:color/holo_red_light"
            android:padding="12dp"
            android:contentDescription="Floating Ball" />
    </androidx.cardview.widget.CardView>

    <!-- 发送按钮 -->
    <Button
        android:id="@+id/send_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="发送"
        android:layout_marginStart="60dp"
        android:visibility="gone" />

</FrameLayout>
