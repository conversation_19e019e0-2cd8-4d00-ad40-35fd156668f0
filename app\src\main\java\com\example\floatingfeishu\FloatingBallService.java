package com.example.floatingfeishu;

import android.animation.ValueAnimator;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ServiceInfo;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.OvershootInterpolator;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.core.app.NotificationCompat;

public class FloatingBallService extends Service {

    private static final String TAG = "FloatingBallService";

    private WindowManager windowManager;
    private View floatingView;
    private WindowManager.LayoutParams params;
    private Button sendButton;
    private ImageView floatingBall;
    private int screenWidth;
    private int screenHeight;
    private boolean isExpanded = false;
    private static final int COLLAPSE_TIMEOUT = 3000; // 3秒后自动收起
    private Handler collapseHandler = new Handler();
    private Runnable collapseRunnable;

    // 前台服务相关常量
    private static final int NOTIFICATION_ID = 1;
    private static final String CHANNEL_ID = "FloatingBallChannel";
    private static final String CHANNEL_NAME = "FloatingBall Service";

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "onStartCommand: 服务启动");
        // 在服务启动时立即创建前台服务
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                createNotificationChannel();
                // 简化前台服务启动方式，不指定类型
                Log.d(TAG, "使用标准前台服务");
                startForeground(NOTIFICATION_ID, createNotification());
            }
        } catch (Exception e) {
            Log.e(TAG, "启动前台服务失败", e);
            Toast.makeText(this, "启动服务失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
            stopSelf();
        }
        return START_STICKY;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "onCreate: 初始化服务");

        try {
            windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);

            // 获取屏幕尺寸
            DisplayMetrics metrics = new DisplayMetrics();
            windowManager.getDefaultDisplay().getMetrics(metrics);
            screenWidth = metrics.widthPixels;
            screenHeight = metrics.heightPixels;
            Log.d(TAG, "屏幕尺寸: " + screenWidth + "x" + screenHeight);
        } catch (Exception e) {
            Log.e(TAG, "获取屏幕尺寸失败", e);
            Toast.makeText(this, "初始化失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
            stopSelf();
            return;
        }

        // 使用简化的悬浮球布局
        try {
            Log.d(TAG, "尝试加载简化悬浮球布局");
            floatingView = LayoutInflater.from(this).inflate(R.layout.simple_floating_ball, null);
        } catch (Exception e) {
            Log.w(TAG, "加载简化悬浮球布局失败，尝试备用布局", e);
            // 如果加载失败，尝试使用备用布局
            try {
                floatingView = LayoutInflater.from(this).inflate(R.layout.layout_floating_ball, null);
            } catch (Exception e2) {
                // 如果两种布局都加载失败，停止服务
                Log.e(TAG, "所有悬浮球布局加载失败", e2);
                Toast.makeText(this, "无法加载悬浮球布局", Toast.LENGTH_LONG).show();
                stopSelf();
                return;
            }
        }

        // 设置窗口参数
        int windowType = (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                ? WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                : WindowManager.LayoutParams.TYPE_PHONE;

        Log.d(TAG, "使用窗口类型: " + windowType);

        params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                windowType,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
                PixelFormat.TRANSLUCENT);

        params.gravity = Gravity.TOP | Gravity.START;
        params.x = 0;
        params.y = 100; // 初始位置在屏幕顶部附近，更容易看到

        // 添加视图到窗口
        try {
            Log.d(TAG, "添加悬浮球到窗口");
            windowManager.addView(floatingView, params);
        } catch (Exception e) {
            // 如果添加视图失败，记录错误并停止服务
            Log.e(TAG, "添加悬浮球到窗口失败", e);
            Toast.makeText(this, "无法显示悬浮球，请检查您是否已授予悬浮窗权限", Toast.LENGTH_LONG).show();
            stopSelf();
            return;
        }

        // 获取视图引用
        floatingBall = floatingView.findViewById(R.id.floating_ball);
        sendButton = floatingView.findViewById(R.id.send_button);

        // 确保悬浮球可见
        if (floatingBall != null) {
            floatingBall.setVisibility(View.VISIBLE);
        }

        // 设置自动收起的Runnable
        collapseRunnable = () -> {
            if (isExpanded) {
                collapseBall();
            }
        };

        // 设置触摸监听器用于拖动
        floatingBall.setOnTouchListener(new View.OnTouchListener() {
            private int initialX;
            private int initialY;
            private float initialTouchX;
            private float initialTouchY;
            private boolean isDragging = false;
            private long touchStartTime;
            private static final int CLICK_THRESHOLD = 200; // 点击判定阈值（毫秒）

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        // 取消自动收起
                        collapseHandler.removeCallbacks(collapseRunnable);

                        initialX = params.x;
                        initialY = params.y;
                        initialTouchX = event.getRawX();
                        initialTouchY = event.getRawY();
                        touchStartTime = System.currentTimeMillis();
                        isDragging = false;
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        // 如果移动距离超过阈值，认为是拖动而非点击
                        if (Math.abs(event.getRawX() - initialTouchX) > 10 ||
                            Math.abs(event.getRawY() - initialTouchY) > 10) {
                            isDragging = true;
                        }

                        params.x = initialX + (int) (event.getRawX() - initialTouchX);
                        params.y = initialY + (int) (event.getRawY() - initialTouchY);
                        windowManager.updateViewLayout(floatingView, params);
                        return true;

                    case MotionEvent.ACTION_UP:
                        // 如果不是拖动且时间短，则视为点击
                        long touchDuration = System.currentTimeMillis() - touchStartTime;
                        if (!isDragging && touchDuration < CLICK_THRESHOLD) {
                            handleBallClick();
                        } else {
                            // 拖动结束后，将球吸附到屏幕边缘
                            snapToEdge();
                        }

                        // 如果展开状态，设置自动收起定时器
                        if (isExpanded) {
                            collapseHandler.postDelayed(collapseRunnable, COLLAPSE_TIMEOUT);
                        }
                        return true;
                }
                return false;
            }
        });

        // 设置发送按钮的点击监听器
        sendButton.setOnClickListener(v -> {
            try {
                ConfigManager configManager = ConfigManager.getInstance(this);

                if (configManager == null) {
                    Toast.makeText(this, "配置管理器初始化失败", Toast.LENGTH_SHORT).show();
                    collapseBall();
                    return;
                }

                String defaultMessage = configManager.getDefaultMessage();
                if (defaultMessage == null || defaultMessage.isEmpty()) {
                    defaultMessage = "默认消息";
                }

                boolean isBotMode = configManager.isBotMode();

                if (isBotMode) {
                    // 应用机器人模式
                    String appId = configManager.getAppId();
                    String appSecret = configManager.getAppSecret();
                    String chatId = configManager.getChatId();

                    if (appId == null || appSecret == null || chatId == null ||
                        appId.isEmpty() || appSecret.isEmpty() || chatId.isEmpty()) {
                        Toast.makeText(this, "请先配置应用机器人信息", Toast.LENGTH_SHORT).show();
                    } else {
                        try {
                            FeishuBotManager botManager = FeishuBotManager.getInstance(this);
                            botManager.setAppCredentials(appId, appSecret);

                            String finalMessage = defaultMessage;
                            botManager.sendMessageToGroup(chatId, finalMessage, true, new FeishuBotManager.MessageCallback() {
                                @Override
                                public void onSuccess(String result) {
                                    configManager.saveLastUsedTime(System.currentTimeMillis());
                                    // 在主线程显示Toast
                                    new Handler(getMainLooper()).post(() ->
                                        Toast.makeText(FloatingBallService.this, "消息已发送", Toast.LENGTH_SHORT).show());

                                    // 记录到多维表格
                                    recordToBitable(finalMessage, "应用机器人", "成功");
                                }

                                @Override
                                public void onError(String error) {
                                    // 在主线程显示Toast
                                    new Handler(getMainLooper()).post(() ->
                                        Toast.makeText(FloatingBallService.this, "发送失败: " + error, Toast.LENGTH_SHORT).show());

                                    // 记录到多维表格
                                    recordToBitable(finalMessage, "应用机器人", "失败: " + error);
                                }
                            });
                        } catch (Exception e) {
                            Toast.makeText(this, "发送失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                            e.printStackTrace();
                        }
                    }
                } else {
                    // Webhook模式
                    String webhookUrl = configManager.getWebhookUrl();

                    if (webhookUrl == null || webhookUrl.isEmpty()) {
                        Toast.makeText(this, "请先配置 Webhook URL", Toast.LENGTH_SHORT).show();
                    } else {
                        try {
                            FeishuHelper.getInstance().sendMessage(webhookUrl, defaultMessage, true);
                            configManager.saveLastUsedTime(System.currentTimeMillis());
                            Toast.makeText(this, "消息已发送", Toast.LENGTH_SHORT).show();

                            // 记录到多维表格
                            recordToBitable(defaultMessage, "Webhook", "成功");

                            // 触发自动化流程
                            triggerAutomation(defaultMessage, "Webhook", "成功");
                        } catch (Exception e) {
                            Toast.makeText(this, "发送失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                            e.printStackTrace();

                            // 记录到多维表格
                            recordToBitable(defaultMessage, "Webhook", "失败: " + e.getMessage());

                            // 触发自动化流程
                            triggerAutomation(defaultMessage, "Webhook", "失败: " + e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                Toast.makeText(this, "发送失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                e.printStackTrace();
            } finally {
                // 无论成功失败，都自动收起
                collapseBall();
            }
        });

        // 初始化后自动吸附到边缘
        new Handler().postDelayed(this::snapToEdge, 500);
    }

    // 处理悬浮球点击事件
    private void handleBallClick() {
        if (isExpanded) {
            collapseBall();
        } else {
            expandBall();
        }
    }

    // 展开悬浮球（显示发送按钮）
    private void expandBall() {
        try {
            if (isExpanded) return; // 已经展开则不重复操作

            isExpanded = true;
            sendButton.setVisibility(View.VISIBLE);

            // 简化动画，直接设置透明度
            sendButton.setAlpha(1f);

            // 设置自动收起定时器
            collapseHandler.postDelayed(collapseRunnable, COLLAPSE_TIMEOUT);
        } catch (Exception e) {
            // 捕获所有异常，防止崩溃
            e.printStackTrace();
        }
    }

    // 收起悬浮球（隐藏发送按钮）
    private void collapseBall() {
        try {
            if (!isExpanded) return; // 已经收起则不重复操作

            isExpanded = false;

            // 直接隐藏按钮，不使用动画
            sendButton.setVisibility(View.GONE);

            // 取消自动收起定时器
            collapseHandler.removeCallbacks(collapseRunnable);
        } catch (Exception e) {
            // 捕获所有异常，防止崩溃
            e.printStackTrace();
        }
    }

    // 将悬浮球吸附到屏幕边缘
    private void snapToEdge() {
        try {
            Log.d(TAG, "尝试将悬浮球吸附到屏幕边缘");
            // 获取悬浮球的宽度
            int ballWidth = floatingBall.getWidth();
            if (ballWidth <= 0) ballWidth = 60; // 默认值增大

            Log.d(TAG, "悬浮球宽度: " + ballWidth);

            // 计算悬浮球中心点
            int centerX = params.x + ballWidth / 2;
            boolean goToRight = centerX > screenWidth / 2;

            // 计算目标位置，确保悬浮球完全可见
            int targetX = goToRight ? screenWidth - ballWidth : 0;

            Log.d(TAG, "目标位置 X: " + targetX + ", 屏幕宽度: " + screenWidth);

            // 使用动画平滑移动到边缘
            final int startX = params.x;
            final int endX = targetX;
            ValueAnimator animator = ValueAnimator.ofInt(startX, endX);
            animator.setDuration(300);
            animator.setInterpolator(new OvershootInterpolator());
            animator.addUpdateListener(animation -> {
                try {
                    params.x = (int) animation.getAnimatedValue();
                    windowManager.updateViewLayout(floatingView, params);
                } catch (Exception e) {
                    Log.e(TAG, "动画更新悬浮球位置失败", e);
                }
            });
            animator.start();

            // 确保收起状态
            if (isExpanded) {
                collapseBall();
            }
        } catch (Exception e) {
            // 捕获所有异常，防止崩溃
            Log.e(TAG, "吸附悬浮球到屏幕边缘失败", e);
        }
    }

    /**
     * 记录操作到多维表格
     */
    private void recordToBitable(String messageContent, String sendMode, String result) {
        try {
            BitableManager bitableManager = BitableManager.getInstance(this);
            bitableManager.recordOperation(messageContent, sendMode, result, new BitableManager.RecordCallback() {
                @Override
                public void onSuccess(String recordResult) {
                    Log.d(TAG, "操作记录到多维表格成功: " + recordResult);
                }

                @Override
                public void onError(String error) {
                    Log.e(TAG, "操作记录到多维表格失败: " + error);
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "记录到多维表格时发生异常", e);
        }
    }

    /**
     * 触发飞书自动化流程
     */
    private void triggerAutomation(String messageContent, String sendMode, String result) {
        try {
            FeishuAutomationHelper automationHelper = FeishuAutomationHelper.getInstance(this);

            // 触发多维表格自动化流程
            automationHelper.triggerBitableAutomation(messageContent, sendMode, result);

            // 同时直接添加记录到多维表格（双重保障）
            automationHelper.addBitableRecord(messageContent, sendMode, result);

            Log.d(TAG, "自动化流程已触发");
        } catch (Exception e) {
            Log.e(TAG, "触发自动化流程时发生异常", e);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (floatingView != null && windowManager != null) {
            collapseHandler.removeCallbacks(collapseRunnable);
            windowManager.removeView(floatingView);
        }
    }

    // 创建通知渠道（Android 8.0及以上版本需要）
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("悬浮球服务通知");
            channel.setShowBadge(false);

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    // 创建前台服务通知
    private Notification createNotification() {
        // 创建点击通知后跳转到主界面的Intent
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, notificationIntent,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0
        );

        // 构建通知
        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("飞书悬浮球")
                .setContentText("点击返回配置界面")
                .setSmallIcon(android.R.drawable.ic_menu_share)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .build();
    }
}
