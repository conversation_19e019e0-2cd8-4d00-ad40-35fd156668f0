# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.3.0"
  }
  digests {
    sha256: "\227\334E\257\357\343\241\344!\332B\270\266\351\371\004\221G|E\374ax >:^\212\005\356\205S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.9.0"
  }
  digests {
    sha256: "\213\332>\343\250\210\207\325Ofy\373kl\327\210b\237s#J\311\034\213\276\331$\347!\354\205\270"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.22"
  }
  digests {
    sha256: "\003\245\303\226\\\303pQ\022\216d\344gH\343\224\266\275L\227\372\201\306\336o\307+\375D\343B\033"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.22"
  }
  digests {
    sha256: "\320\3026^$7\357p\363E\206\325\017\005WC\367\227\026\274\376e\344\274r9\315\322f\236\367\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.4"
  }
  digests {
    sha256: "?\334\016\355[\304\270>\351b\'tR\n-\262Tp7\016\254\321X\034\254\0367pO\t[\000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.4"
  }
  digests {
    sha256: "\302L\213\262{\263 \304\2518qP\032~^\fa`v8\220{\031z\357gU\023\324\310 \276"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.1"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.1"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.1"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.1"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.1"
  }
  digests {
    sha256: "\310/\211\"\032\333\341\235\367\307\255\272\266?N\314\205\177\307F\343\311IBV\253\217\245\302\004\221\262"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: "\025B\241\337{\351\b\311_\356\221\270\333\300?\331t\365?\021\330J\205\330\201\371ZRU\034@Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.10.0"
  }
  digests {
    sha256: "- N\344\361\026\271\210\246\246\265\333\214\364=\343\370\247M\n\341\272\027Xo\220\252\343\216>\032-"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.10.0"
  }
  digests {
    sha256: "u\200\361O\241i\022\006\343p\201\255?\222\006;\026\003\263(\332\v\263\026\362\376\360.\005b\347\354"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.0.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.0.0"
  }
  digests {
    sha256: "\276d\240\314\037(\352\234\325\311p\335~uW\257r\310\b\3278\304\225\263\227\277\211|\231!\351\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.security"
    artifactId: "security-crypto"
    version: "1.1.0-alpha06"
  }
  digests {
    sha256: "\227a\021\027\v?\023\322\360\371E}\237\237W\223G\024<\266\\\005Be\220m3\307a\212L*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.crypto.tink"
    artifactId: "tink-android"
    version: "1.8.0"
  }
  digests {
    sha256: "^\376\217\034\266\347X\024\256\006y=_\370\331\260\036o\210\357\227\354\300b\321N\274\'\002}\277x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.9"
  }
  digests {
    sha256: "\323\231\222\221\205]\344\225\311Lt7a\270\253Qv\317\352\276(\032Z\260\330\350\324S&\375p>"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 32
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 11
  library_dep_index: 26
  library_dep_index: 44
  library_dep_index: 29
  library_dep_index: 6
  library_dep_index: 32
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 11
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 29
  library_dep_index: 25
  library_dep_index: 6
}
library_dependencies {
  library_index: 3
  library_dep_index: 2
}
library_dependencies {
  library_index: 4
  library_dep_index: 2
  library_dep_index: 5
  library_dep_index: 3
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 31
  library_dep_index: 28
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 9
  library_dep_index: 2
  library_dep_index: 10
}
library_dependencies {
  library_index: 11
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 30
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 12
  library_dep_index: 2
}
library_dependencies {
  library_index: 13
  library_dep_index: 2
  library_dep_index: 12
}
library_dependencies {
  library_index: 14
  library_dep_index: 2
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 11
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 15
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 7
}
library_dependencies {
  library_index: 18
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 6
}
library_dependencies {
  library_index: 21
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 22
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 11
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 22
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 11
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 23
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 24
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 11
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 24
  library_dep_index: 2
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 2
}
library_dependencies {
  library_index: 26
  library_dep_index: 2
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 11
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 2
  library_dep_index: 28
  library_dep_index: 22
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 14
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 11
  library_dep_index: 26
}
library_dependencies {
  library_index: 28
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 4
}
library_dependencies {
  library_index: 29
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 6
}
library_dependencies {
  library_index: 30
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 24
  library_dep_index: 10
}
library_dependencies {
  library_index: 31
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 32
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 0
}
library_dependencies {
  library_index: 33
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 34
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 3
}
library_dependencies {
  library_index: 35
  library_dep_index: 2
}
library_dependencies {
  library_index: 36
  library_dep_index: 2
}
library_dependencies {
  library_index: 37
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 39
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 40
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 39
}
library_dependencies {
  library_index: 41
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 1
  library_dep_index: 22
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 5
}
library_dependencies {
  library_index: 42
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 38
}
library_dependencies {
  library_index: 43
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 21
  library_dep_index: 26
}
library_dependencies {
  library_index: 44
  library_dep_index: 2
}
library_dependencies {
  library_index: 45
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 46
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
  library_dep_index: 49
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 0
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 45
  library_dep_index: 4
  library_dep_index: 37
  library_dep_index: 52
  library_dep_index: 5
  library_dep_index: 41
  library_dep_index: 11
  library_dep_index: 57
  library_dep_index: 44
  library_dep_index: 58
  library_dep_index: 33
  library_dep_index: 59
}
library_dependencies {
  library_index: 48
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 50
  library_dep_index: 2
}
library_dependencies {
  library_index: 51
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 38
  library_dep_index: 3
}
library_dependencies {
  library_index: 52
  library_dep_index: 4
  library_dep_index: 3
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 54
  library_dep_index: 43
  library_dep_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 54
  library_dep_index: 2
}
library_dependencies {
  library_index: 55
  library_dep_index: 2
}
library_dependencies {
  library_index: 56
  library_dep_index: 2
}
library_dependencies {
  library_index: 57
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 38
  library_dep_index: 3
}
library_dependencies {
  library_index: 58
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 59
  library_dep_index: 2
  library_dep_index: 41
  library_dep_index: 57
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
  library_dep_index: 6
}
library_dependencies {
  library_index: 61
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 19
  library_dep_index: 7
}
library_dependencies {
  library_index: 63
  library_dep_index: 2
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 45
  dependency_index: 47
  dependency_index: 60
  dependency_index: 63
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
