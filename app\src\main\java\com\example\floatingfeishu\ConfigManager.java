package com.example.floatingfeishu;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;


@SuppressLint("ApplySharedPref")
public class ConfigManager {

    private static final String TAG = "ConfigManager";
    private static final String PREF_NAME = "FloatingFeishuConfig";
    private static final String KEY_WEBHOOK_URL = "webhook_url";
    private static final String KEY_DEFAULT_MESSAGE = "default_message";
    private static final String KEY_LAST_USED = "last_used";

    // 应用机器人相关配置
    private static final String KEY_APP_ID = "app_id";
    private static final String KEY_APP_SECRET = "app_secret";
    private static final String KEY_CHAT_ID = "chat_id";
    private static final String KEY_BOT_MODE = "bot_mode"; // true: 应用机器人模式, false: webhook模式

    // 多维表格相关配置
    private static final String KEY_BITABLE_ENABLED = "bitable_enabled";
    private static final String KEY_BITABLE_APP_TOKEN = "bitable_app_token";
    private static final String KEY_BITABLE_TABLE_ID = "bitable_table_id";
    private static final String KEY_BITABLE_TIME_FIELD = "bitable_time_field";
    private static final String KEY_BITABLE_MESSAGE_FIELD = "bitable_message_field";
    private static final String KEY_BITABLE_MODE_FIELD = "bitable_mode_field";
    private static final String KEY_BITABLE_RESULT_FIELD = "bitable_result_field";
    private static final String KEY_BITABLE_DEVICE_FIELD = "bitable_device_field";
    private static final String KEY_BITABLE_NOTE_FIELD = "bitable_note_field";

    // 自动化流程相关配置
    private static final String KEY_AUTOMATION_WEBHOOK_URL = "automation_webhook_url";
    private static final String KEY_AUTOMATION_ENABLED = "automation_enabled";
    private static final String KEY_AUTOMATION_SECRET = "automation_secret";

    private static ConfigManager instance;
    private final SharedPreferences sharedPreferences;

    private ConfigManager(Context context) {
        // 直接使用普通的SharedPreferences
        this.sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    public static synchronized ConfigManager getInstance(Context context) {
        if (instance == null) {
            instance = new ConfigManager(context);
        }
        return instance;
    }

    public void saveWebhookUrl(String url) {
        try {
            // 直接存储原始值，不进行加密
            sharedPreferences.edit()
                    .putString(KEY_WEBHOOK_URL, url)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving webhook URL", e);
        }
    }

    public String getWebhookUrl() {
        try {
            // 直接返回存储的值
            return sharedPreferences.getString(KEY_WEBHOOK_URL, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting webhook URL", e);
            return null;
        }
    }

    public void saveDefaultMessage(String message) {
        try {
            sharedPreferences.edit().putString(KEY_DEFAULT_MESSAGE, message).apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving webhook URL", e);
        }
    }

    public String getDefaultMessage() {
        try {
            return sharedPreferences.getString(KEY_DEFAULT_MESSAGE, "");
        } catch (Exception e) {
            Log.e(TAG, "Error getting default message", e);
            return "";
        }
    }

    public void saveLastUsedTime(long time) {
        try {
            sharedPreferences.edit().putLong(KEY_LAST_USED, time).apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving last used time", e);
        }
    }

    @SuppressWarnings("unused")
    public long getLastUsedTime() {
        try {
            return sharedPreferences.getLong(KEY_LAST_USED, 0);
        } catch (Exception e) {
            Log.e(TAG, "Error getting last used time", e);
            return 0;
        }
    }

    // 应用机器人相关方法

    /**
     * 保存应用凭证
     */
    public void saveAppCredentials(String appId, String appSecret) {
        try {
            sharedPreferences.edit()
                    .putString(KEY_APP_ID, appId)
                    .putString(KEY_APP_SECRET, appSecret)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving app credentials", e);
        }
    }

    /**
     * 获取应用ID
     */
    public String getAppId() {
        try {
            return sharedPreferences.getString(KEY_APP_ID, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting app ID", e);
            return null;
        }
    }

    /**
     * 获取应用密钥
     */
    public String getAppSecret() {
        try {
            return sharedPreferences.getString(KEY_APP_SECRET, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting app secret", e);
            return null;
        }
    }

    /**
     * 保存群组ID
     */
    @SuppressWarnings("unused")
    public void saveChatId(String chatId) {
        try {
            sharedPreferences.edit()
                    .putString(KEY_CHAT_ID, chatId)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving chat ID", e);
        }
    }

    /**
     * 获取群组ID
     */
    public String getChatId() {
        try {
            return sharedPreferences.getString(KEY_CHAT_ID, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting chat ID", e);
            return null;
        }
    }

    /**
     * 设置机器人模式
     */
    @SuppressWarnings("unused")
    public void setBotMode(boolean isBotMode) {
        try {
            sharedPreferences.edit()
                    .putBoolean(KEY_BOT_MODE, isBotMode)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error setting bot mode", e);
        }
    }

    /**
     * 获取机器人模式
     */
    public boolean isBotMode() {
        try {
            return sharedPreferences.getBoolean(KEY_BOT_MODE, false);
        } catch (Exception e) {
            Log.e(TAG, "Error getting bot mode", e);
            return false;
        }
    }

    // 多维表格相关方法

    /**
     * 设置多维表格启用状态
     */
    @SuppressWarnings("unused")
    public void setBitableEnabled(boolean enabled) {
        try {
            sharedPreferences.edit()
                    .putBoolean(KEY_BITABLE_ENABLED, enabled)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error setting bitable enabled", e);
        }
    }

    /**
     * 获取多维表格启用状态
     */
    public boolean isBitableEnabled() {
        try {
            return sharedPreferences.getBoolean(KEY_BITABLE_ENABLED, false);
        } catch (Exception e) {
            Log.e(TAG, "Error getting bitable enabled", e);
            return false;
        }
    }

    /**
     * 保存多维表格配置
     */
    @SuppressWarnings("unused")
    public void saveBitableConfig(String appToken, String tableId) {
        try {
            sharedPreferences.edit()
                    .putString(KEY_BITABLE_APP_TOKEN, appToken)
                    .putString(KEY_BITABLE_TABLE_ID, tableId)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving bitable config", e);
        }
    }

    /**
     * 获取多维表格App Token
     */
    public String getBitableAppToken() {
        try {
            return sharedPreferences.getString(KEY_BITABLE_APP_TOKEN, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting bitable app token", e);
            return null;
        }
    }

    /**
     * 获取多维表格Table ID
     */
    public String getBitableTableId() {
        try {
            return sharedPreferences.getString(KEY_BITABLE_TABLE_ID, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting bitable table id", e);
            return null;
        }
    }

    /**
     * 保存多维表格字段映射
     */
    @SuppressWarnings("unused")
    public void saveBitableFieldMapping(String timeField, String messageField, String modeField,
                                       String resultField, String deviceField, String noteField) {
        try {
            sharedPreferences.edit()
                    .putString(KEY_BITABLE_TIME_FIELD, timeField)
                    .putString(KEY_BITABLE_MESSAGE_FIELD, messageField)
                    .putString(KEY_BITABLE_MODE_FIELD, modeField)
                    .putString(KEY_BITABLE_RESULT_FIELD, resultField)
                    .putString(KEY_BITABLE_DEVICE_FIELD, deviceField)
                    .putString(KEY_BITABLE_NOTE_FIELD, noteField)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving bitable field mapping", e);
        }
    }

    // 获取各个字段名称的方法
    public String getBitableTimeField() {
        return sharedPreferences.getString(KEY_BITABLE_TIME_FIELD, "操作时间");
    }

    public String getBitableMessageField() {
        return sharedPreferences.getString(KEY_BITABLE_MESSAGE_FIELD, "消息内容");
    }

    public String getBitableModeField() {
        return sharedPreferences.getString(KEY_BITABLE_MODE_FIELD, "发送模式");
    }

    public String getBitableResultField() {
        return sharedPreferences.getString(KEY_BITABLE_RESULT_FIELD, "操作结果");
    }

    public String getBitableDeviceField() {
        return sharedPreferences.getString(KEY_BITABLE_DEVICE_FIELD, "设备信息");
    }

    public String getBitableNoteField() {
        return sharedPreferences.getString(KEY_BITABLE_NOTE_FIELD, "备注");
    }

    // 自动化流程相关方法

    /**
     * 设置自动化流程启用状态
     */
    public void setAutomationEnabled(boolean enabled) {
        try {
            sharedPreferences.edit()
                    .putBoolean(KEY_AUTOMATION_ENABLED, enabled)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error setting automation enabled", e);
        }
    }

    /**
     * 获取自动化流程启用状态
     */
    public boolean isAutomationEnabled() {
        try {
            return sharedPreferences.getBoolean(KEY_AUTOMATION_ENABLED, false);
        } catch (Exception e) {
            Log.e(TAG, "Error getting automation enabled", e);
            return false;
        }
    }

    /**
     * 保存自动化Webhook URL
     */
    public void saveAutomationWebhookUrl(String url) {
        try {
            sharedPreferences.edit()
                    .putString(KEY_AUTOMATION_WEBHOOK_URL, url)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving automation webhook URL", e);
        }
    }

    /**
     * 获取自动化Webhook URL
     */
    public String getAutomationWebhookUrl() {
        try {
            return sharedPreferences.getString(KEY_AUTOMATION_WEBHOOK_URL, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting automation webhook URL", e);
            return null;
        }
    }

    /**
     * 保存自动化密钥
     */
    public void saveAutomationSecret(String secret) {
        try {
            sharedPreferences.edit()
                    .putString(KEY_AUTOMATION_SECRET, secret)
                    .apply();
        } catch (Exception e) {
            Log.e(TAG, "Error saving automation secret", e);
        }
    }

    /**
     * 获取自动化密钥
     */
    public String getAutomationSecret() {
        try {
            return sharedPreferences.getString(KEY_AUTOMATION_SECRET, null);
        } catch (Exception e) {
            Log.e(TAG, "Error getting automation secret", e);
            return null;
        }
    }
}
