# 飞书自动化测试脚本 (PowerShell版本)
# 用于测试向飞书自动化Webhook发送数据，验证多维表格是否能正确接收和记录数据

param(
    [switch]$Suite,
    [string]$TestCase = "basic"
)

# 飞书自动化Webhook URL
$WebhookUrl = "https://www.feishu.cn/flow/api/trigger-webhook/da2bf33efb73fe27bf6477c27c27d106"

function New-TestPayload {
    param(
        [string]$TestType = "basic"
    )
    
    $timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds()
    $datetime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    $basePayload = @{
        timestamp = $timestamp
        datetime = $datetime
        device_model = "Test Device (PowerShell)"
        device_brand = "Windows Script"
        android_version = "PowerShell Test"
        app_version = "1.0.0-test"
    }
    
    switch ($TestType) {
        "basic" {
            $basePayload.message = "🧪 基础测试消息 - PowerShell脚本测试"
            $basePayload.mode = "自动化测试"
            $basePayload.result = "测试成功"
        }
        "success" {
            $basePayload.message = "✅ 成功案例测试 - 消息发送成功"
            $basePayload.mode = "Webhook"
            $basePayload.result = "成功"
        }
        "failure" {
            $basePayload.message = "❌ 失败案例测试 - 模拟发送失败"
            $basePayload.mode = "Webhook"
            $basePayload.result = "失败: 网络连接超时"
        }
        "emergency" {
            $basePayload.message = "🚨 紧急测试消息 - 需要立即处理！"
            $basePayload.mode = "紧急通知"
            $basePayload.result = "成功"
            $basePayload.priority = "high"
            $basePayload.tags = @("紧急", "测试", "自动化")
        }
    }
    
    return $basePayload
}

function Send-WebhookRequest {
    param(
        [hashtable]$Payload,
        [int]$TimeoutSeconds = 30
    )
    
    try {
        Write-Host "📤 发送数据到: $WebhookUrl" -ForegroundColor Cyan
        
        $jsonPayload = $Payload | ConvertTo-Json -Depth 10
        Write-Host "📋 数据内容:" -ForegroundColor Yellow
        Write-Host $jsonPayload -ForegroundColor Gray
        
        $headers = @{
            'Content-Type' = 'application/json'
            'User-Agent' = 'FloatingFeishu-Test-PowerShell/1.0'
        }
        
        $response = Invoke-RestMethod -Uri $WebhookUrl -Method Post -Body $jsonPayload -Headers $headers -TimeoutSec $TimeoutSeconds
        
        Write-Host "✅ 请求发送成功！" -ForegroundColor Green
        
        if ($response) {
            Write-Host "📄 响应内容:" -ForegroundColor Yellow
            Write-Host ($response | ConvertTo-Json -Depth 10) -ForegroundColor Gray
        }
        
        return $true
    }
    catch {
        Write-Host "❌ 发送失败: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($_.Exception.Response) {
            Write-Host "📊 响应状态码: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
        
        return $false
    }
}

function Invoke-TestSuite {
    Write-Host "🚀 开始飞书自动化测试套件" -ForegroundColor Green
    Write-Host ("=" * 50) -ForegroundColor Gray
    
    $testCases = @(
        @{ Type = "basic"; Description = "基础功能测试" },
        @{ Type = "success"; Description = "成功案例测试" },
        @{ Type = "failure"; Description = "失败案例测试" },
        @{ Type = "emergency"; Description = "紧急消息测试" }
    )
    
    $results = @()
    
    for ($i = 0; $i -lt $testCases.Count; $i++) {
        $testCase = $testCases[$i]
        $testNum = $i + 1
        
        Write-Host "`n🧪 测试 $testNum/$($testCases.Count): $($testCase.Description)" -ForegroundColor Cyan
        Write-Host ("-" * 30) -ForegroundColor Gray
        
        $payload = New-TestPayload -TestType $testCase.Type
        $success = Send-WebhookRequest -Payload $payload
        
        $results += @{
            Description = $testCase.Description
            Success = $success
        }
        
        if ($i -lt ($testCases.Count - 1)) {
            Write-Host "⏳ 等待3秒后进行下一个测试..." -ForegroundColor Yellow
            Start-Sleep -Seconds 3
        }
    }
    
    # 输出测试结果汇总
    Write-Host "`n$("=" * 50)" -ForegroundColor Gray
    Write-Host "📊 测试结果汇总:" -ForegroundColor Green
    Write-Host ("=" * 50) -ForegroundColor Gray
    
    $successCount = 0
    foreach ($result in $results) {
        $status = if ($result.Success) { "✅ 成功" } else { "❌ 失败" }
        $color = if ($result.Success) { "Green" } else { "Red" }
        Write-Host "$($result.Description): $status" -ForegroundColor $color
        if ($result.Success) { $successCount++ }
    }
    
    Write-Host "`n📈 总体结果: $successCount/$($results.Count) 个测试通过" -ForegroundColor Cyan
    
    if ($successCount -eq $results.Count) {
        Write-Host "🎉 所有测试都通过了！请检查飞书多维表格是否有新记录。" -ForegroundColor Green
    } else {
        Write-Host "⚠️  部分测试失败，请检查网络连接和Webhook URL。" -ForegroundColor Yellow
    }
    
    return $successCount -eq $results.Count
}

function Invoke-SingleTest {
    param([string]$TestType = "basic")
    
    Write-Host "🧪 单个测试模式" -ForegroundColor Cyan
    Write-Host ("=" * 30) -ForegroundColor Gray
    
    $payload = New-TestPayload -TestType $TestType
    $success = Send-WebhookRequest -Payload $payload
    
    if ($success) {
        Write-Host "`n✅ 测试完成！请到飞书多维表格检查是否有新记录添加。" -ForegroundColor Green
        Write-Host "📋 检查要点:" -ForegroundColor Yellow
        Write-Host "   - 操作时间是否正确" -ForegroundColor Gray
        Write-Host "   - 消息内容是否完整" -ForegroundColor Gray
        Write-Host "   - 发送模式是否正确" -ForegroundColor Gray
        Write-Host "   - 操作结果是否正确" -ForegroundColor Gray
        Write-Host "   - 设备信息是否为'Test Device (PowerShell)'" -ForegroundColor Gray
    } else {
        Write-Host "`n❌ 测试失败，请检查:" -ForegroundColor Red
        Write-Host "   - 网络连接是否正常" -ForegroundColor Gray
        Write-Host "   - Webhook URL是否正确" -ForegroundColor Gray
        Write-Host "   - 飞书自动化流程是否已启用" -ForegroundColor Gray
    }
    
    return $success
}

# 主程序
function Main {
    Write-Host "🤖 飞书自动化测试工具 (PowerShell版)" -ForegroundColor Green
    Write-Host ("=" * 50) -ForegroundColor Gray
    Write-Host "此工具将向飞书自动化Webhook发送测试数据" -ForegroundColor Cyan
    Write-Host "请确保您已经:" -ForegroundColor Yellow
    Write-Host "1. ✅ 创建了飞书多维表格" -ForegroundColor Gray
    Write-Host "2. ✅ 配置了自动化流程" -ForegroundColor Gray
    Write-Host "3. ✅ 设置了Webhook触发器" -ForegroundColor Gray
    Write-Host "4. ✅ 启用了自动化流程" -ForegroundColor Gray
    Write-Host ("=" * 50) -ForegroundColor Gray
    
    $success = $false
    
    if ($Suite) {
        # 运行完整测试套件
        $success = Invoke-TestSuite
    } else {
        # 运行单个测试
        $success = Invoke-SingleTest -TestType $TestCase
    }
    
    Write-Host "`n$("=" * 50)" -ForegroundColor Gray
    Write-Host "📝 使用说明:" -ForegroundColor Yellow
    Write-Host "   .\test_feishu_automation.ps1                    # 运行单个基础测试" -ForegroundColor Gray
    Write-Host "   .\test_feishu_automation.ps1 -Suite            # 运行完整测试套件" -ForegroundColor Gray
    Write-Host "   .\test_feishu_automation.ps1 -TestCase success # 运行指定类型测试" -ForegroundColor Gray
    Write-Host ("=" * 50) -ForegroundColor Gray
    
    if ($success) {
        Write-Host "🎯 下一步：请登录飞书查看多维表格是否有新记录！" -ForegroundColor Green
    }
    
    return $success
}

# 执行主程序
$result = Main
if (-not $result) {
    exit 1
}
