<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.1.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.1.0)" variant="all" version="8.1.0">

    <issue
        id="UnknownIssueId"
        message="Unknown issue id &quot;Deprecation&quot;"
        errorLine1="    &lt;issue id=&quot;Deprecation&quot; severity=&quot;warning&quot; />"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="lint.xml"
            line="9"
            column="5"/>
    </issue>

    <issue
        id="UnknownIssueId"
        message="Unknown issue id &quot;Deprecation&quot;"
        errorLine1="    &lt;issue id=&quot;Deprecation&quot; severity=&quot;warning&quot; />"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="lint.xml"
            line="9"
            column="5"/>
    </issue>

    <issue
        id="OldTargetApi"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details."
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="build.gradle"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="            floatingView = LayoutInflater.from(this).inflate(R.layout.simple_floating_ball, null);"
        errorLine2="                                                                                            ~~~~">
        <location
            file="src/main/java/com/example/floatingfeishu/FloatingBallService.java"
            line="99"
            column="93"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="                floatingView = LayoutInflater.from(this).inflate(R.layout.layout_floating_ball, null);"
        errorLine2="                                                                                                ~~~~">
        <location
            file="src/main/java/com/example/floatingfeishu/FloatingBallService.java"
            line="104"
            column="97"/>
    </issue>

    <issue
        id="UseSwitchCompatOrMaterialXml"
        message="Use `SwitchCompat` from AppCompat or `SwitchMaterial` from Material library"
        errorLine1="                    &lt;Switch"
        errorLine2="                    ^">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="277"
            column="21"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/Theme.FloatingFeishu`)"
        errorLine1="    android:background=&quot;#f5f5f5&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#F5F5F5` with a theme that also paints a background (inferred theme is `@style/Theme.FloatingFeishu`)"
        errorLine1="    android:background=&quot;#F5F5F5&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive icon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MonochromeLauncherIcon"
        message="The application adaptive roundIcon is missing a monochrome tag"
        errorLine1="&lt;adaptive-icon xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="ButtonStyle"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="345"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="357"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="53"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="70"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="135"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="152"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="108"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="154"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="171"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="188"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                &lt;EditText"
        errorLine2="                 ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="232"
            column="18"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="298"
            column="22"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="                    &lt;EditText"
        errorLine2="                     ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main_enhanced.xml"
            line="315"
            column="22"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="Custom view ``ImageView`` has `setOnTouchListener` called on it but does not override `performClick`"
        errorLine1="        floatingBall.setOnTouchListener(new View.OnTouchListener() {"
        errorLine2="        ^">
        <location
            file="src/main/java/com/example/floatingfeishu/FloatingBallService.java"
            line="161"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        message="`onTouch` should call `View#performClick` when a click is detected"
        errorLine1="            public boolean onTouch(View v, MotionEvent event) {"
        errorLine2="                           ~~~~~~~">
        <location
            file="src/main/java/com/example/floatingfeishu/FloatingBallService.java"
            line="171"
            column="28"/>
    </issue>

</issues>
