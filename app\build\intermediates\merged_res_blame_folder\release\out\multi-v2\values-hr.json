{"logs": [{"outputFile": "com.example.floatingfeishu.app-mergeReleaseResources-30:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\38fdf4e56534b3c4c6a8297bf7eeddee\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,8650", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,8730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ecca92267eba45ce3e43e495f0e1965d\\transformed\\material-1.10.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2945,3036,3114,3170,3225,3291,3365,3443,3531,3613,3685,3762,3842,3916,4023,4116,4189,4281,4377,4451,4527,4623,4675,4757,4824,4911,4998,5060,5124,5187,5257,5363,5479,5576,5690,5750,5809", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2940,3031,3109,3165,3220,3286,3360,3438,3526,3608,3680,3757,3837,3911,4018,4111,4184,4276,4372,4446,4522,4618,4670,4752,4819,4906,4993,5055,5119,5182,5252,5358,5474,5571,5685,5745,5804,5884"}, "to": {"startLines": "2,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3081,3159,3237,3322,3419,3512,3608,3738,3822,3890,3986,4054,4117,4225,4285,4351,4407,4478,4538,4592,4718,4775,4837,4891,4966,5100,5185,5266,5403,5487,5573,5706,5797,5875,5931,5986,6052,6126,6204,6292,6374,6446,6523,6603,6677,6784,6877,6950,7042,7138,7212,7288,7384,7436,7518,7585,7672,7759,7821,7885,7948,8018,8124,8240,8337,8451,8511,8570", "endLines": "6,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,132,90,77,55,54,65,73,77,87,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79", "endOffsets": "365,3154,3232,3317,3414,3507,3603,3733,3817,3885,3981,4049,4112,4220,4280,4346,4402,4473,4533,4587,4713,4770,4832,4886,4961,5095,5180,5261,5398,5482,5568,5701,5792,5870,5926,5981,6047,6121,6199,6287,6369,6441,6518,6598,6672,6779,6872,6945,7037,7133,7207,7283,7379,7431,7513,7580,7667,7754,7816,7880,7943,8013,8119,8235,8332,8446,8506,8565,8645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cf89ab1ea7cc19352d4f47103c74320d\\transformed\\core-1.9.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "103", "startColumns": "4", "startOffsets": "8735", "endColumns": "100", "endOffsets": "8831"}}]}]}