<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!-- 悬浮球 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/ball_container"
        android:layout_width="48dp"
        android:layout_height="48dp"
        app:cardCornerRadius="24dp"
        app:cardElevation="4dp"
        android:alpha="0.85">

        <ImageView
            android:id="@+id/floating_ball"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@android:drawable/ic_menu_share"
            android:background="@drawable/bg_floating_ball"
            android:padding="12dp"
            android:scaleType="centerInside"
            android:contentDescription="Floating Ball" />
    </androidx.cardview.widget.CardView>

    <!-- 发送按钮 -->
    <Button
        android:id="@+id/send_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="发送"
        android:layout_marginStart="52dp"
        android:backgroundTint="#673AB7"
        android:textColor="#FFFFFF"
        android:visibility="gone" />

</FrameLayout>
