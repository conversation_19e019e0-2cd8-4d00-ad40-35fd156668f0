package com.example.floatingfeishu;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {

    private ActivityResultLauncher<Intent> permissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // setContentView(R.layout.activity_main); // Remove setContentView here

        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (Settings.canDrawOverlays(this)) {
                            setupUI(); // Call setupUI after permission is granted
                        } else {
                            Toast.makeText(this, "权限被拒绝，无法显示悬浮窗", Toast.LENGTH_SHORT).show();
                        }
                    }
                }
        );

        checkOverlayPermission();
    }

    private void setupUI() {
        setContentView(R.layout.activity_main); // Set content view here

        EditText webhookUrlInput = findViewById(R.id.webhook_url_input);
        EditText defaultMessageInput = findViewById(R.id.default_message_input);
        Button saveConfigButton = findViewById(R.id.save_config_button);

        try {
            ConfigManager configManager = ConfigManager.getInstance(this);

            // Load saved config
            String savedWebhookUrl = configManager.getWebhookUrl();
            String savedDefaultMessage = configManager.getDefaultMessage();

            if (savedWebhookUrl != null) {
                webhookUrlInput.setText(savedWebhookUrl);
            }
            if (savedDefaultMessage != null) {
                defaultMessageInput.setText(savedDefaultMessage);
            }

            // Set up click listener for the save button
            saveConfigButton.setOnClickListener(v -> {
                try {
                    String webhookUrl = webhookUrlInput.getText().toString();
                    String defaultMessage = defaultMessageInput.getText().toString();

                    configManager.saveWebhookUrl(webhookUrl);
                    configManager.saveDefaultMessage(defaultMessage);

                    Toast.makeText(this, "配置已保存", Toast.LENGTH_SHORT).show();
                } catch (Exception e) {
                    Toast.makeText(this, "保存配置失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    e.printStackTrace();
                }
            });

        } catch (Exception e) {
            Toast.makeText(this, "初始化配置管理器失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }

        startFloatingBallService(); // Start the service after setting up the UI
    }

    private void checkOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + getPackageName()));
                permissionLauncher.launch(intent);
            } else {
                setupUI(); // Call setupUI if permission is already granted
            }
        } else {
            setupUI(); // Call setupUI for older Android versions
        }
    }

    private void startFloatingBallService() {
        Intent intent = new Intent(this, FloatingBallService.class);
        startService(intent);
        //finish(); // Do not finish the activity, so user can still access the config UI
    }
}
