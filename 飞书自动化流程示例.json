{"automation_flow_examples": {"bitable_automation": {"name": "FloatingFeishu操作记录自动化", "description": "自动记录FloatingFeishu应用的消息发送操作到多维表格", "trigger": {"type": "webhook", "webhook_url": "https://open.feishu.cn/open-apis/automation/v1/webhook/YOUR_WEBHOOK_ID", "verification_token": "YOUR_VERIFICATION_TOKEN", "secret": "YOUR_SECRET_KEY"}, "conditions": [{"field": "result", "operator": "is_not_empty", "description": "确保操作结果不为空"}], "actions": [{"type": "add_record", "table_id": "YOUR_TABLE_ID", "field_mapping": {"操作时间": {"type": "datetime", "value": "{{timestamp}}"}, "消息内容": {"type": "text", "value": "{{message}}"}, "发送模式": {"type": "single_select", "value": "{{mode}}"}, "操作结果": {"type": "single_select", "value": "{{result}}"}, "设备信息": {"type": "text", "value": "{{device_model}} (Android {{android_version}})"}, "应用版本": {"type": "text", "value": "{{app_version}}"}, "备注": {"type": "text", "value": "通过FloatingFeishu悬浮球发送"}}}, {"type": "send_notification", "condition": {"field": "result", "operator": "contains", "value": "失败"}, "notification": {"type": "feishu_message", "chat_id": "ADMIN_CHAT_ID", "message": "FloatingFeishu发送失败：{{message}} - {{result}}"}}]}, "bot_assistant_automation": {"name": "FloatingFeishu智能助手", "description": "基于消息内容的智能响应和处理", "trigger": {"type": "webhook", "webhook_url": "https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_BOT_WEBHOOK_ID"}, "conditions": [{"field": "message", "operator": "contains", "value": "紧急", "description": "检测紧急消息"}], "actions": [{"type": "send_message", "chat_type": "group", "chat_id": "EMERGENCY_GROUP_ID", "message": {"msg_type": "interactive", "card": {"header": {"title": {"content": "🚨 紧急通知", "tag": "plain_text"}, "template": "red"}, "elements": [{"tag": "div", "text": {"content": "来自FloatingFeishu的紧急消息：\n{{message}}", "tag": "lark_md"}}, {"tag": "div", "text": {"content": "发送时间：{{datetime}}\n设备：{{device_model}}", "tag": "lark_md"}}, {"tag": "action", "actions": [{"tag": "button", "text": {"content": "已处理", "tag": "plain_text"}, "type": "primary", "value": {"action": "mark_handled", "message_id": "{{message_id}}"}}]}]}}}]}, "statistics_automation": {"name": "使用统计自动化", "description": "定期生成使用统计报告", "trigger": {"type": "schedule", "cron": "0 9 * * 1", "description": "每周一上午9点执行"}, "actions": [{"type": "query_records", "table_id": "YOUR_TABLE_ID", "filter": {"conditions": [{"field": "操作时间", "operator": "is_within", "value": "last_7_days"}]}, "output_variable": "weekly_records"}, {"type": "calculate_statistics", "input": "{{weekly_records}}", "calculations": [{"type": "count", "field": "all", "output": "total_messages"}, {"type": "count", "field": "操作结果", "filter": "成功", "output": "success_count"}, {"type": "count", "field": "操作结果", "filter": "失败", "output": "failure_count"}, {"type": "percentage", "numerator": "{{success_count}}", "denominator": "{{total_messages}}", "output": "success_rate"}]}, {"type": "send_message", "chat_id": "ADMIN_CHAT_ID", "message": {"msg_type": "interactive", "card": {"header": {"title": {"content": "📊 FloatingFeishu 周报", "tag": "plain_text"}, "template": "blue"}, "elements": [{"tag": "div", "text": {"content": "**本周使用统计**\n\n📱 总消息数：{{total_messages}}\n✅ 成功发送：{{success_count}}\n❌ 发送失败：{{failure_count}}\n📈 成功率：{{success_rate}}%", "tag": "lark_md"}}, {"tag": "hr"}, {"tag": "div", "text": {"content": "点击查看详细数据", "tag": "plain_text"}}, {"tag": "action", "actions": [{"tag": "button", "text": {"content": "查看多维表格", "tag": "plain_text"}, "type": "default", "url": "https://your-bitable-url"}]}]}}}]}}, "webhook_payload_examples": {"basic_message": {"timestamp": 1640995200000, "datetime": "2024-01-01 12:00:00", "message": "测试消息", "mode": "Webhook", "result": "成功", "device_model": "Pixel 6", "device_brand": "Google", "android_version": "12", "app_version": "1.0.0", "signature": "sha256_hash_if_secret_configured"}, "emergency_message": {"timestamp": 1640995200000, "datetime": "2024-01-01 12:00:00", "message": "紧急：服务器异常，需要立即处理！", "mode": "Webhook", "result": "成功", "device_model": "Samsung Galaxy S21", "device_brand": "Samsung", "android_version": "11", "app_version": "1.0.0", "priority": "high", "tags": ["紧急", "服务器", "异常"]}, "failure_message": {"timestamp": 1640995200000, "datetime": "2024-01-01 12:00:00", "message": "定时提醒消息", "mode": "Webhook", "result": "失败: 网络连接超时", "device_model": "Xiaomi Mi 11", "device_brand": "<PERSON><PERSON>", "android_version": "12", "app_version": "1.0.0", "error_code": "NETWORK_TIMEOUT", "retry_count": 3}}, "field_configurations": {"bitable_fields": {"操作时间": {"type": "datetime", "required": true, "description": "消息发送的时间戳"}, "消息内容": {"type": "text", "required": true, "max_length": 1000, "description": "发送的消息内容"}, "发送模式": {"type": "single_select", "required": true, "options": ["Webhook", "应用机器人", "自动化测试"], "description": "消息发送的方式"}, "操作结果": {"type": "single_select", "required": true, "options": ["成功", "失败", "部分成功"], "description": "发送操作的结果"}, "设备信息": {"type": "text", "required": false, "description": "发送设备的型号和系统信息"}, "应用版本": {"type": "text", "required": false, "description": "FloatingFeishu应用的版本号"}, "备注": {"type": "text", "required": false, "description": "其他补充信息"}, "错误代码": {"type": "text", "required": false, "description": "失败时的错误代码"}, "重试次数": {"type": "number", "required": false, "min": 0, "max": 10, "description": "失败时的重试次数"}}}, "integration_tips": {"security": ["使用HTTPS确保数据传输安全", "配置验证密钥防止恶意请求", "定期更新Webhook URL和密钥", "限制IP访问范围"], "performance": ["使用异步处理避免阻塞", "实现请求重试机制", "设置合理的超时时间", "监控API调用频率"], "monitoring": ["设置自动化流程执行监控", "配置异常告警通知", "定期检查数据完整性", "分析使用趋势和模式"], "maintenance": ["定期清理历史数据", "备份重要配置信息", "更新自动化流程逻辑", "优化字段映射配置"]}}