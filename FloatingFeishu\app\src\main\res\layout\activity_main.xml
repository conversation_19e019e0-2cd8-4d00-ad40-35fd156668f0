<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/floating_ball"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:src="@android:drawable/ic_menu_share"
        android:contentDescription="Floating Ball" />

    <Button
        android:id="@+id/send_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="发送"
        android:visibility="gone" />

    <EditText
        android:id="@+id/webhook_url_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Webhook URL"
        android:layout_marginTop="60dp"
         />

    <EditText
        android:id="@+id/default_message_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="默认消息"
        android:layout_marginTop="10dp"/>

    <Button
        android:id="@+id/save_config_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="保存配置"
        android:layout_marginTop="10dp"/>

</FrameLayout>
