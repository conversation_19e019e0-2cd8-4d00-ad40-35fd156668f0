package com.example.floatingfeishu;

import android.app.Service;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.IBinder;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;

public class FloatingBallService extends Service {

    private WindowManager windowManager;
    private View floatingView;
    private WindowManager.LayoutParams params;
    private Button sendButton;

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();

        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);

        // Inflate the floating ball layout
        floatingView = LayoutInflater.from(this).inflate(R.layout.activity_main, null);


        // Setup layout parameters
        int windowType = (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                ? WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                : WindowManager.LayoutParams.TYPE_PHONE;

        params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                windowType,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT);

        params.gravity = Gravity.TOP | Gravity.START;
        params.x = 0;
        params.y = 100;

        // Add the view to the window
        windowManager.addView(floatingView, params);

        // Get references to views
        ImageView floatingBall = floatingView.findViewById(R.id.floating_ball);
        sendButton = floatingView.findViewById(R.id.send_button);


        // Set up touch listener for dragging
        if (floatingBall != null) {
            floatingBall.setOnTouchListener(new View.OnTouchListener() {
                private int initialX;
                private int initialY;
                private float initialTouchX;
                private float initialTouchY;

                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            initialX = params.x;
                            initialY = params.y;
                            initialTouchX = event.getRawX();
                            initialTouchY = event.getRawY();
                            return true;
                        case MotionEvent.ACTION_MOVE:
                            params.x = initialX + (int) (event.getRawX() - initialTouchX);
                            params.y = initialY + (int) (event.getRawY() - initialTouchY);
                            windowManager.updateViewLayout(floatingView, params);
                            return true;
                    }
                    return false;
                }
            });
            // Show the send button when the floating ball is clicked
            floatingBall.setOnClickListener(v -> {
                sendButton.setVisibility(View.VISIBLE);
            });
        }

        // Set up click listener for the send button
        sendButton.setOnClickListener(v -> {
            try {
                ConfigManager configManager = ConfigManager.getInstance(this);
                String webhookUrl = configManager.getWebhookUrl();
                String defaultMessage = configManager.getDefaultMessage();

                if (webhookUrl == null || webhookUrl.isEmpty()) {
                    Toast.makeText(this, "请先配置 Webhook URL", Toast.LENGTH_SHORT).show();
                } else {
                    FeishuHelper.getInstance().sendMessage(webhookUrl, defaultMessage != null ? defaultMessage : "默认消息", true);
                    configManager.saveLastUsedTime(System.currentTimeMillis());
                }
            } catch (Exception e) {
                Toast.makeText(this, "发送失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                e.printStackTrace();
            }
            sendButton.setVisibility(View.GONE); // Hide the button after sending
        });

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (floatingView != null) {
            windowManager.removeView(floatingView);
        }
    }
}
