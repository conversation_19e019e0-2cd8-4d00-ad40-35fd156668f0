http://schemas.android.com/apk/res-auto;;${\:app*debug*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*sourceProvider*0*resDir*0}/values/ic_launcher_background.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*debug*sourceProvider*0*resDir*0}/drawable/bg_floating_ball.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_main_enhanced.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/floating_ball.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/layout_floating_ball.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/simple_floating_ball.xml,${\:app*debug*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*debug*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*debug*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*sourceProvider*0*resDir*0}/values/themes.xml,+color:colorPrimary,0,V"#6200EE";colorPrimaryDark,0,V"#3700B3";colorAccent,0,V"#03DAC5";ic_launcher_background,1,V"#3DDC84";+drawable:ic_launcher_foreground,2,F;bg_floating_ball,3,F;+id:bot_config_card,4,F;app_id_input,4,F;floating_ball,5,F;floating_ball,6,F;floating_ball,7,F;mode_radio_group,4,F;app_secret_input,4,F;test_bitable_button,4,F;webhook_mode_radio,4,F;automation_secret_input,8,F;automation_webhook_input,8,F;test_automation_button,8,F;bitable_enable_switch,4,F;chat_id_input,4,F;default_message_input,8,F;default_message_input,4,F;test_send_button,8,F;test_send_button,4,F;bitable_config_layout,4,F;save_automation_button,8,F;send_button,5,F;send_button,6,F;send_button,7,F;webhook_config_card,4,F;bitable_app_token_input,4,F;bot_mode_radio,4,F;automation_enabled_checkbox,8,F;save_config_button,8,F;save_config_button,4,F;webhook_url_input,8,F;webhook_url_input,4,F;ball_container,5,F;ball_container,6,F;ball_container,7,F;bitable_table_id_input,4,F;+layout:floating_ball,5,F;layout_floating_ball,6,F;simple_floating_ball,7,F;activity_main,8,F;activity_main_enhanced,4,F;+mipmap:ic_launcher_round,9,F;ic_launcher,10,F;+string:app_name,11,V"FloatingFeishu";+style:Theme.FloatingFeishu,12,VDTheme.MaterialComponents.Light.DarkActionBar,colorPrimary:@color/colorPrimary,colorPrimaryDark:@color/colorPrimaryDark,colorAccent:@color/colorAccent,;