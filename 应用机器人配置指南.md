# FloatingFeishu 应用机器人配置指南

## 概述

本指南将帮助您配置FloatingFeishu应用的应用机器人模式，这是一种更安全、更强大的飞书集成方式。

## 应用机器人 vs Webhook模式对比

| 特性 | Webhook模式 | 应用机器人模式 |
|------|-------------|----------------|
| 配置复杂度 | 简单 | 中等 |
| 安全性 | 中等 | 高 |
| 功能丰富度 | 基础 | 丰富 |
| 权限管理 | 有限 | 精细 |
| 事件订阅 | 不支持 | 支持 |
| 认证方式 | URL Token | OAuth 2.0 |

## 第一步：创建飞书应用

### 1.1 访问飞书开发者后台
1. 打开浏览器，访问 [飞书开发者后台](https://open.feishu.cn/app)
2. 使用您的飞书账号登录

### 1.2 创建新应用
1. 点击"创建应用"按钮
2. 选择"自建应用"
3. 填写应用信息：
   - **应用名称**：FloatingFeishu
   - **应用描述**：悬浮飞书消息发送工具
   - **应用图标**：上传应用图标（可选）
4. 点击"创建"

### 1.3 获取应用凭证
创建成功后，您将看到：
- **App ID**：应用唯一标识符
- **App Secret**：应用密钥

**重要**：请妥善保管App Secret，不要泄露给他人。

## 第二步：配置应用权限

### 2.1 开启机器人能力
1. 在应用管理页面，点击"机器人"选项卡
2. 点击"启用机器人"
3. 配置机器人信息：
   - **机器人名称**：FloatingFeishu Bot
   - **机器人描述**：快速发送消息的悬浮工具
   - **机器人头像**：上传头像（可选）

### 2.2 申请权限
在"权限管理"页面，申请以下权限：

**必需权限**：
- `im:message` - 获取与发送单聊、群组消息
- `im:message:send_as_bot` - 以应用的身份发送消息

**可选权限**：
- `im:chat` - 获取群组信息
- `im:chat:readonly` - 获取用户所在的群组列表

### 2.3 配置事件订阅（可选）
如果需要接收群组事件：
1. 在"事件订阅"页面，配置请求网址
2. 订阅以下事件：
   - `im.message.receive_v1` - 接收消息
   - `im.chat.updated_v1` - 群组信息变更

## 第三步：获取群组ID

### 3.1 方法一：通过飞书客户端
1. 在飞书客户端中，进入目标群组
2. 点击群组名称，进入群组设置
3. 在群组设置中找到"群组ID"或"Chat ID"

### 3.2 方法二：通过API获取
使用以下API获取用户所在的群组列表：
```
GET https://open.feishu.cn/open-apis/im/v1/chats
```

### 3.3 方法三：通过机器人加入群组
1. 将机器人添加到目标群组
2. 机器人会收到加入群组的事件，包含群组ID

## 第四步：在应用中配置

### 4.1 打开FloatingFeishu应用
1. 启动FloatingFeishu应用
2. 确保已授予悬浮窗权限

### 4.2 选择应用机器人模式
1. 在配置界面中，选择"应用机器人模式"
2. 填写以下信息：
   - **应用ID**：从开发者后台获取的App ID
   - **应用密钥**：从开发者后台获取的App Secret
   - **群组ID**：目标群组的Chat ID
   - **默认消息**：快速发送的默认消息内容

### 4.3 测试配置
1. 点击"测试发送"按钮
2. 检查目标群组是否收到测试消息
3. 如果成功，点击"保存配置"

## 第五步：使用应用机器人

### 5.1 日常使用
配置完成后，使用方式与Webhook模式相同：
1. 悬浮球会显示在屏幕边缘
2. 点击悬浮球展开发送按钮
3. 点击发送按钮快速发送消息

### 5.2 优势体验
应用机器人模式提供以下优势：
- **更安全**：使用OAuth 2.0认证，无需暴露Webhook URL
- **更稳定**：官方API支持，稳定性更高
- **更丰富**：支持更多消息类型和交互功能
- **更智能**：可以接收群组事件，实现双向交互

## 故障排除

### 常见问题

**Q1：获取token失败**
- 检查App ID和App Secret是否正确
- 确认应用状态是否为"已启用"
- 检查网络连接是否正常

**Q2：发送消息失败**
- 确认机器人是否已加入目标群组
- 检查群组ID是否正确
- 确认应用是否有发送消息权限

**Q3：权限申请被拒绝**
- 联系企业管理员审批权限
- 确认权限申请理由是否充分
- 检查应用是否符合企业安全策略

### 调试技巧

1. **查看日志**：在Android Studio中查看应用日志
2. **API测试**：使用Postman等工具测试飞书API
3. **权限检查**：在开发者后台检查权限状态
4. **网络诊断**：确认网络连接和防火墙设置

## 安全建议

1. **保护密钥**：不要在代码中硬编码App Secret
2. **权限最小化**：只申请必需的权限
3. **定期更新**：定期更新应用密钥
4. **监控使用**：监控API调用频率和异常

## 总结

应用机器人模式为FloatingFeishu提供了更强大、更安全的飞书集成能力。虽然配置稍微复杂，但带来的好处是显著的。建议在生产环境中使用应用机器人模式，以获得最佳的安全性和功能体验。

如果您在配置过程中遇到问题，请参考飞书开放平台文档或联系技术支持。
