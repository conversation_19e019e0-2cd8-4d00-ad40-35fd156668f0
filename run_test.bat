@echo off
chcp 65001 >nul
echo.
echo 🤖 飞书自动化测试工具
echo ================================
echo.
echo 选择测试方式:
echo 1. Python脚本测试 (推荐)
echo 2. PowerShell脚本测试
echo 3. 快速单个测试 (Python)
echo 4. 完整测试套件 (Python)
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo.
    echo 🐍 运行Python测试脚本...
    python test_feishu_automation.py
    goto end
)

if "%choice%"=="2" (
    echo.
    echo 💻 运行PowerShell测试脚本...
    powershell -ExecutionPolicy Bypass -File test_feishu_automation.ps1
    goto end
)

if "%choice%"=="3" (
    echo.
    echo ⚡ 快速单个测试...
    python test_feishu_automation.py
    goto end
)

if "%choice%"=="4" (
    echo.
    echo 🧪 完整测试套件...
    python test_feishu_automation.py --suite
    goto end
)

if "%choice%"=="5" (
    echo 👋 再见！
    goto end
)

echo ❌ 无效选择，请重新运行脚本
goto end

:end
echo.
echo 📋 测试完成后请检查:
echo    1. 飞书多维表格是否有新记录
echo    2. 记录的时间、内容是否正确
echo    3. 自动化流程是否正常触发
echo.
pause
