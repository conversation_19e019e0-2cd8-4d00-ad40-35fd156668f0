1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.floatingfeishu"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 必须权限 -->
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:5:5-78
12-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:5:22-75
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:6:5-77
13-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:6:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:7:5-87
14-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:7:22-84
15
16    <!-- 可选权限 -->
17    <uses-permission android:name="android.permission.INTERNET" />
17-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:10:5-67
17-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:10:22-64
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:11:5-79
18-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:11:22-76
19
20    <permission
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
21        android:name="com.example.floatingfeishu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.example.floatingfeishu.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
25
26    <application
26-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:13:5-36:19
27        android:allowBackup="true"
27-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:14:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\cf89ab1ea7cc19352d4f47103c74320d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
29        android:debuggable="true"
30        android:extractNativeLibs="true"
31        android:icon="@mipmap/ic_launcher"
31-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:15:9-43
32        android:label="@string/app_name"
32-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:16:9-41
33        android:roundIcon="@mipmap/ic_launcher_round"
33-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:17:9-54
34        android:supportsRtl="true"
34-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:18:9-35
35        android:theme="@style/Theme.FloatingFeishu" >
35-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:19:9-52
36        <service
36-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:21:9-26:19
37            android:name="com.example.floatingfeishu.FloatingBallService"
37-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:22:13-48
38            android:enabled="true"
38-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:23:13-35
39            android:exported="false"
39-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:24:13-37
40            android:foregroundServiceType="dataSync" >
40-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:25:13-53
41        </service>
42
43        <activity
43-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:28:9-35:20
44            android:name="com.example.floatingfeishu.MainActivity"
44-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:29:13-41
45            android:exported="true" >
45-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:30:13-36
46            <intent-filter>
46-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:31:13-34:29
47                <action android:name="android.intent.action.MAIN" />
47-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:32:17-69
47-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:32:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:33:17-77
49-->E:\FloatingFeishu\app\src\main\AndroidManifest.xml:33:27-74
50            </intent-filter>
51        </activity>
52
53        <provider
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.example.floatingfeishu.androidx-startup"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b9996c08011e841824064a663be72df\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\914a02d4ef33b4a2d3f9bc481d83e7fc\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\914a02d4ef33b4a2d3f9bc481d83e7fc\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\914a02d4ef33b4a2d3f9bc481d83e7fc\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <receiver
68-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
69            android:name="androidx.profileinstaller.ProfileInstallReceiver"
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
70            android:directBootAware="false"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
71            android:enabled="true"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
72            android:exported="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
73            android:permission="android.permission.DUMP" >
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
75                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
78                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
79            </intent-filter>
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
81                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
84                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb0329a3fb69eb4cdd5ec3d8e97f80d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
85            </intent-filter>
86        </receiver>
87    </application>
88
89</manifest>
