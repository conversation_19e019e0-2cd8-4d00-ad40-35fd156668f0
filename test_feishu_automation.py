#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书自动化测试脚本
用于测试向飞书自动化Webhook发送数据，验证多维表格是否能正确接收和记录数据
"""

import requests
import json
import time
from datetime import datetime
import hashlib
import sys

# 飞书自动化Webhook URL
WEBHOOK_URL = "https://www.feishu.cn/flow/api/trigger-webhook/da2bf33efb73fe27bf6477c27c27d106"

def generate_signature(data, secret=""):
    """生成签名（如果需要验证）"""
    if not secret:
        return ""
    
    input_str = data + secret
    return hashlib.sha256(input_str.encode('utf-8')).hexdigest()

def create_test_payload(test_case="basic"):
    """创建测试数据载荷"""
    timestamp = int(time.time() * 1000)
    datetime_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    base_payload = {
        "timestamp": timestamp,
        "datetime": datetime_str,
        "device_model": "Test Device",
        "device_brand": "Python Script",
        "android_version": "Test",
        "app_version": "1.0.0-test"
    }
    
    if test_case == "basic":
        base_payload.update({
            "message": "🧪 基础测试消息 - Python脚本测试",
            "mode": "自动化测试",
            "result": "测试成功"
        })
    elif test_case == "success":
        base_payload.update({
            "message": "✅ 成功案例测试 - 消息发送成功",
            "mode": "Webhook",
            "result": "成功"
        })
    elif test_case == "failure":
        base_payload.update({
            "message": "❌ 失败案例测试 - 模拟发送失败",
            "mode": "Webhook", 
            "result": "失败: 网络连接超时"
        })
    elif test_case == "emergency":
        base_payload.update({
            "message": "🚨 紧急测试消息 - 需要立即处理！",
            "mode": "紧急通知",
            "result": "成功",
            "priority": "high",
            "tags": ["紧急", "测试", "自动化"]
        })
    
    return base_payload

def send_webhook_request(payload, timeout=30):
    """发送Webhook请求"""
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'FloatingFeishu-Test/1.0'
    }
    
    try:
        print(f"📤 发送数据到: {WEBHOOK_URL}")
        print(f"📋 数据内容: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        response = requests.post(
            WEBHOOK_URL,
            json=payload,
            headers=headers,
            timeout=timeout
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.text:
            try:
                response_json = response.json()
                print(f"📄 响应内容: {json.dumps(response_json, ensure_ascii=False, indent=2)}")
            except:
                print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 请求发送成功！")
            return True
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 连接错误")
        return False
    except Exception as e:
        print(f"❌ 发送失败: {str(e)}")
        return False

def run_test_suite():
    """运行完整的测试套件"""
    print("🚀 开始飞书自动化测试")
    print("=" * 50)
    
    test_cases = [
        ("basic", "基础功能测试"),
        ("success", "成功案例测试"),
        ("failure", "失败案例测试"),
        ("emergency", "紧急消息测试")
    ]
    
    results = []
    
    for i, (test_case, description) in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}/{len(test_cases)}: {description}")
        print("-" * 30)
        
        payload = create_test_payload(test_case)
        success = send_webhook_request(payload)
        results.append((description, success))
        
        if i < len(test_cases):
            print("⏳ 等待3秒后进行下一个测试...")
            time.sleep(3)
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    success_count = 0
    for description, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{description}: {status}")
        if success:
            success_count += 1
    
    print(f"\n📈 总体结果: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试都通过了！请检查飞书多维表格是否有新记录。")
    else:
        print("⚠️  部分测试失败，请检查网络连接和Webhook URL。")
    
    return success_count == len(results)

def run_single_test():
    """运行单个测试"""
    print("🧪 单个测试模式")
    print("=" * 30)
    
    payload = create_test_payload("basic")
    success = send_webhook_request(payload)
    
    if success:
        print("\n✅ 测试完成！请到飞书多维表格检查是否有新记录添加。")
        print("📋 检查要点:")
        print("   - 操作时间是否正确")
        print("   - 消息内容是否完整")
        print("   - 发送模式是否为'自动化测试'")
        print("   - 操作结果是否为'测试成功'")
        print("   - 设备信息是否为'Test Device'")
    else:
        print("\n❌ 测试失败，请检查:")
        print("   - 网络连接是否正常")
        print("   - Webhook URL是否正确")
        print("   - 飞书自动化流程是否已启用")
    
    return success

def main():
    """主函数"""
    print("🤖 飞书自动化测试工具")
    print("=" * 50)
    print("此工具将向飞书自动化Webhook发送测试数据")
    print("请确保您已经:")
    print("1. ✅ 创建了飞书多维表格")
    print("2. ✅ 配置了自动化流程")
    print("3. ✅ 设置了Webhook触发器")
    print("4. ✅ 启用了自动化流程")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--suite":
        # 运行完整测试套件
        success = run_test_suite()
    else:
        # 运行单个测试
        success = run_single_test()
    
    print("\n" + "=" * 50)
    print("📝 使用说明:")
    print("   python test_feishu_automation.py          # 运行单个测试")
    print("   python test_feishu_automation.py --suite  # 运行完整测试套件")
    print("=" * 50)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
