# FloatingFeishu 飞书自动化配置指南

## 概述

本指南将帮助您配置FloatingFeishu应用与飞书自动化功能的集成，实现消息发送的自动化记录和处理。

## 飞书自动化功能介绍

飞书自动化功能包括：
1. **多维表格自动化流程** - 通过Webhook触发器自动记录数据
2. **机器人助手** - 自动化消息处理和响应
3. **集成平台** - 连接不同应用和服务

## 第一步：创建飞书自动化流程

### 1.1 创建多维表格自动化

1. **打开飞书多维表格**
   - 登录飞书，创建或打开一个多维表格
   - 点击右上角的"自动化"按钮

2. **创建自动化流程**
   - 点击"+ 新建自动化"
   - 选择"Webhook触发器"作为触发条件

3. **配置Webhook触发器**
   - 触发器类型：选择"Webhook"
   - 获取Webhook URL（类似：`https://open.feishu.cn/open-apis/bot/v2/hook/xxx`）
   - 设置验证密钥（可选，用于安全验证）

4. **配置执行操作**
   - 选择"添加记录"操作
   - 配置字段映射：
     - 操作时间 ← `{{timestamp}}`
     - 消息内容 ← `{{message}}`
     - 发送模式 ← `{{mode}}`
     - 操作结果 ← `{{result}}`
     - 设备信息 ← `{{device_model}}`

### 1.2 创建机器人助手自动化

1. **打开飞书机器人助手**
   - 在飞书中搜索"机器人助手"
   - 点击"+ 新建机器人应用"

2. **配置触发条件**
   - 选择"Webhook触发器"
   - 获取机器人助手的Webhook URL

3. **配置执行操作**
   - 添加"发送消息"操作
   - 配置消息模板和发送对象

## 第二步：在FloatingFeishu中配置自动化

### 2.1 基础配置

1. **打开FloatingFeishu应用**
2. **配置基础信息**：
   - Webhook URL：填入飞书机器人的Webhook地址
   - 默认消息：设置快速发送的消息内容

### 2.2 自动化流程配置

1. **启用自动化流程**
   - 勾选"启用自动化流程"复选框

2. **配置自动化Webhook URL**
   - 填入步骤1.1中获取的多维表格自动化Webhook URL
   - 格式示例：`https://open.feishu.cn/open-apis/automation/v1/webhook/xxx`

3. **配置验证密钥（可选）**
   - 如果在飞书中设置了验证密钥，请在此处填入相同的密钥
   - 用于确保请求的安全性

4. **保存配置**
   - 点击"保存自动化配置"按钮

### 2.3 测试配置

1. **测试基础发送**
   - 点击"测试发送消息"按钮
   - 检查飞书群组是否收到测试消息

2. **测试自动化连接**
   - 点击"测试自动化连接"按钮
   - 检查多维表格是否自动添加了测试记录

## 第三步：高级配置选项

### 3.1 自定义字段映射

在多维表格自动化流程中，您可以自定义字段映射：

```json
{
  "timestamp": "{{timestamp}}",
  "datetime": "{{datetime}}",
  "message": "{{message}}",
  "mode": "{{mode}}",
  "result": "{{result}}",
  "device_model": "{{device_model}}",
  "device_brand": "{{device_brand}}",
  "android_version": "{{android_version}}",
  "app_version": "{{app_version}}"
}
```

### 3.2 条件过滤

您可以在自动化流程中添加条件过滤：
- 只记录成功的发送操作
- 过滤特定的消息内容
- 根据设备类型进行分类

### 3.3 多重操作

配置多个执行操作：
1. 添加记录到多维表格
2. 发送通知到管理员
3. 更新统计数据
4. 触发其他自动化流程

## 第四步：数据分析和监控

### 4.1 创建数据仪表盘

1. **在多维表格中创建视图**
   - 按时间统计发送频率
   - 按结果统计成功率
   - 按设备统计使用情况

2. **创建图表**
   - 时间趋势图
   - 成功率饼图
   - 设备分布柱状图

### 4.2 设置监控告警

1. **配置异常告警**
   - 发送失败率过高时告警
   - 长时间无操作时提醒
   - 异常设备访问告警

2. **定期报告**
   - 每日使用统计
   - 每周趋势分析
   - 每月总结报告

## 第五步：故障排查

### 5.1 常见问题

1. **自动化流程未触发**
   - 检查Webhook URL是否正确
   - 确认网络连接正常
   - 验证密钥是否匹配

2. **数据记录不完整**
   - 检查字段映射配置
   - 确认数据格式正确
   - 验证权限设置

3. **性能问题**
   - 检查自动化流程复杂度
   - 优化触发频率
   - 减少不必要的操作

### 5.2 调试方法

1. **查看日志**
   - 在Android Studio中查看应用日志
   - 检查网络请求和响应

2. **测试连接**
   - 使用测试按钮验证配置
   - 手动发送测试数据

3. **分步验证**
   - 先测试基础Webhook
   - 再测试自动化流程
   - 最后验证完整流程

## 第六步：最佳实践

### 6.1 安全建议

1. **使用HTTPS**
   - 确保所有Webhook URL使用HTTPS
   - 配置验证密钥

2. **权限控制**
   - 最小权限原则
   - 定期更新密钥

3. **数据保护**
   - 不在消息中包含敏感信息
   - 定期清理历史数据

### 6.2 性能优化

1. **批量处理**
   - 合并多个操作
   - 减少API调用频率

2. **缓存策略**
   - 缓存访问令牌
   - 复用网络连接

3. **错误处理**
   - 实现重试机制
   - 优雅降级处理

## 总结

通过以上配置，您的FloatingFeishu应用将能够：

1. **自动记录操作** - 每次发送消息都会自动记录到多维表格
2. **实时监控** - 通过仪表盘实时查看使用情况
3. **智能分析** - 基于历史数据进行趋势分析
4. **异常告警** - 及时发现和处理异常情况

这样就实现了完整的飞书自动化集成，让您的消息发送更加智能和高效！

## 技术支持

如果在配置过程中遇到问题，请：
1. 查看应用日志获取详细错误信息
2. 检查飞书自动化流程的执行历史
3. 参考飞书开放平台文档
4. 联系技术支持获取帮助
