<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="FloatingFeishu 自动化配置"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#673AB7"
        android:layout_marginBottom="24dp" />

    <!-- 配置界面 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="基础配置"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Webhook URL:"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />

            <EditText
                android:id="@+id/webhook_url_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入飞书机器人的Webhook URL"
                android:inputType="textUri"
                android:layout_marginBottom="16dp"
                android:background="@android:drawable/edit_text"
                android:padding="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="默认消息:"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />

            <EditText
                android:id="@+id/default_message_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入默认发送的消息内容"
                android:inputType="textMultiLine"
                android:minLines="2"
                android:layout_marginBottom="16dp"
                android:background="@android:drawable/edit_text"
                android:padding="8dp" />

            <Button
                android:id="@+id/save_config_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="保存基础配置"
                android:backgroundTint="#673AB7"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/test_send_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试发送消息"
                android:backgroundTint="#FF9800" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 自动化流程配置 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="自动化流程配置"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <CheckBox
                android:id="@+id/automation_enabled_checkbox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="启用自动化流程"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="自动化Webhook URL:"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />

            <EditText
                android:id="@+id/automation_webhook_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入飞书自动化流程的Webhook URL"
                android:inputType="textUri"
                android:layout_marginBottom="16dp"
                android:background="@android:drawable/edit_text"
                android:padding="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="验证密钥 (可选):"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />

            <EditText
                android:id="@+id/automation_secret_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入验证密钥（用于签名验证）"
                android:inputType="textPassword"
                android:layout_marginBottom="16dp"
                android:background="@android:drawable/edit_text"
                android:padding="8dp" />

            <Button
                android:id="@+id/save_automation_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="保存自动化配置"
                android:backgroundTint="#4CAF50"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/test_automation_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="测试自动化连接"
                android:backgroundTint="#FF9800" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 使用说明 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="使用说明"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="1. 配置基础的Webhook URL和默认消息"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2. 启用自动化流程，配置自动化Webhook URL"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="3. 测试各项连接确保配置正确"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="4. 悬浮球会显示在屏幕上"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="5. 使用悬浮球发送消息，系统将自动记录到多维表格"
                android:layout_marginTop="4dp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>
</ScrollView>
