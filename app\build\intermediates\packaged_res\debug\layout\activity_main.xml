<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="FloatingFeishu"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#673AB7"
        android:layout_marginBottom="24dp" />

    <!-- 配置界面 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Webhook配置"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Webhook URL:"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />

            <EditText
                android:id="@+id/webhook_url_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入飞书机器人的Webhook URL"
                android:inputType="textUri"
                android:layout_marginBottom="16dp"
                android:background="@android:drawable/edit_text"
                android:padding="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="默认消息:"
                android:textStyle="bold"
                android:layout_marginTop="8dp" />

            <EditText
                android:id="@+id/default_message_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="请输入默认发送的消息内容"
                android:inputType="textMultiLine"
                android:minLines="2"
                android:layout_marginBottom="16dp"
                android:background="@android:drawable/edit_text"
                android:padding="8dp" />

            <Button
                android:id="@+id/save_config_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="保存配置"
                android:backgroundTint="#673AB7" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 使用说明 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="使用说明"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="1. 配置飞书机器人的Webhook URL"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2. 设置默认消息内容"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="3. 点击保存配置"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="4. 悬浮球会显示在屏幕上"
                android:layout_marginTop="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="5. 点击悬浮球，再点击发送按钮即可发送消息"
                android:layout_marginTop="4dp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>
